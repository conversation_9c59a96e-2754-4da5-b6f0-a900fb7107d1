#!/bin/bash
# SSLyze SSL/TLS Server Scanner
# This script uses SS<PERSON><PERSON><PERSON> to perform a comprehensive scan of the SSL/TLS configuration of a server.

set -e

# Configuration
DOMAIN=${1:-"chat.stage.divinci.app"}
ORIGIN_IP=${2:-"************"}
PORT=${3:-"443"}
OUTPUT_DIR="./test-results"
mkdir -p "$OUTPUT_DIR"

echo "=== SSLyze SSL/TLS Server Scanner ==="
echo "Domain: $DOMAIN"
echo "Origin IP: $ORIGIN_IP"
echo "Port: $PORT"
echo

# Check if SSLyze is installed
if ! command -v sslyze &> /dev/null; then
  echo "❌ SSLyze is not installed. Please install it with:"
  echo "   uv pip install sslyze"

  # Try to install SSLyze
  echo "Attempting to install SSLyze..."
  uv pip install sslyze || {
    echo "❌ Failed to install SSLyze. Please install it manually."
    exit 1
  }
fi

# Step 1: Scan the domain
echo "Step 1: Scanning $DOMAIN..."
sslyze --regular "$DOMAIN" > "$OUTPUT_DIR/sslyze_domain_scan.txt"
echo "✅ Domain scan completed and saved to $OUTPUT_DIR/sslyze_domain_scan.txt"

# Step 2: Scan the origin IP
echo "Step 2: Scanning $ORIGIN_IP:$PORT..."
sslyze --regular "$ORIGIN_IP:$PORT" --sni="$DOMAIN" > "$OUTPUT_DIR/sslyze_origin_scan.txt"
echo "✅ Origin scan completed and saved to $OUTPUT_DIR/sslyze_origin_scan.txt"

# Step 3: Check for certificate issues
echo "Step 3: Checking for certificate issues..."
CERT_ISSUES=$(grep -A 20 "CERTIFICATE INFO" "$OUTPUT_DIR/sslyze_domain_scan.txt")
echo "$CERT_ISSUES" > "$OUTPUT_DIR/sslyze_cert_issues.txt"

if echo "$CERT_ISSUES" | grep -q "NOT TRUSTED"; then
  echo "❌ Certificate is not trusted"
else
  echo "✅ Certificate is trusted"
fi

# Step 4: Check for protocol support
echo "Step 4: Checking for protocol support..."
PROTOCOLS=$(grep -A 20 "SCAN RESULTS FOR" "$OUTPUT_DIR/sslyze_domain_scan.txt" | grep -E "TLS |SSL ")
echo "$PROTOCOLS" > "$OUTPUT_DIR/sslyze_protocols.txt"

if echo "$PROTOCOLS" | grep -q "TLS 1.2.*ACCEPTED"; then
  echo "✅ TLS 1.2 is supported"
else
  echo "❌ TLS 1.2 is not supported"
fi

if echo "$PROTOCOLS" | grep -q "TLS 1.3.*ACCEPTED"; then
  echo "✅ TLS 1.3 is supported"
else
  echo "❌ TLS 1.3 is not supported"
fi

# Step 5: Check for cipher suite support
echo "Step 5: Checking for cipher suite support..."
CIPHERS=$(grep -A 50 "CIPHER SUITES" "$OUTPUT_DIR/sslyze_domain_scan.txt")
echo "$CIPHERS" > "$OUTPUT_DIR/sslyze_ciphers.txt"

if echo "$CIPHERS" | grep -q "ECDHE-RSA-AES128-GCM-SHA256.*ACCEPTED"; then
  echo "✅ ECDHE-RSA-AES128-GCM-SHA256 is supported"
else
  echo "❌ ECDHE-RSA-AES128-GCM-SHA256 is not supported"
fi

# Step 6: Check for vulnerabilities
echo "Step 6: Checking for vulnerabilities..."
VULNERABILITIES=$(grep -A 50 "VULNERABILITIES" "$OUTPUT_DIR/sslyze_domain_scan.txt")
echo "$VULNERABILITIES" > "$OUTPUT_DIR/sslyze_vulnerabilities.txt"

if echo "$VULNERABILITIES" | grep -q "VULNERABLE"; then
  echo "❌ Vulnerabilities found"
  grep -A 2 "VULNERABLE" "$OUTPUT_DIR/sslyze_vulnerabilities.txt"
else
  echo "✅ No vulnerabilities found"
fi

# Step 7: Compare domain and origin scans
echo "Step 7: Comparing domain and origin scans..."
diff -y --suppress-common-lines "$OUTPUT_DIR/sslyze_domain_scan.txt" "$OUTPUT_DIR/sslyze_origin_scan.txt" > "$OUTPUT_DIR/sslyze_diff.txt" || true
echo "✅ Comparison saved to $OUTPUT_DIR/sslyze_diff.txt"

echo
echo "=== SSLyze SSL/TLS Server Scanner Complete ==="
echo "Results saved to $OUTPUT_DIR/"
