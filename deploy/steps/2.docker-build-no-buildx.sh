#!/bin/bash -x
set -e
set -o pipefail

# shellcheck disable=SC2016
: '
📖 Script to build and push Docker images for changed services in the repository. This script uses `service-info.js`
to obtain necessary service configuration information, eliminating the need for `jq`.

▶️ Usage:
  ./2.docker-build.sh <changed_folders_csv> <tag>

🎛️ Arguments:
  <changed_folders_csv>   A comma-separated list of folders with changes.
  <tag>                   The environment tag (e.g., develop, stage, prod). If not provided, defaults to "latest".

🌎 Environment:
  - Requires `service-info.js` located in "deploy/util/" to fetch service details.
<<<<<<< HEAD
  - Uses standard `docker` commands to build and push images to Google Container Registry.
=======
  - Uses `docker` to build and push images to Google Container Registry.
>>>>>>> origin/develop

🔢 Steps:
  1. **Build Docker Images**: Builds Docker images for the specified services using configuration data from `service-info.js`.
  2. **Push Docker Images**: Pushes the built Docker images to Google Container Registry.
  3. **Handle Docker Cache**: Prunes Docker images and containers to free up space, excluding essential images.

🔗 Dependencies:
  - Node.js: To execute `service-info.js`.
  - Docker: To build and push images.

📓 Notes:
  - Ensure `gcloud` is authenticated for `docker` to push to the Google Container Registry.
  - This script includes parallel processing for building and pushing images to speed up the process.
<<<<<<< HEAD
'

START_TIME=$(date +%s)
echo "2️⃣ 🐋 🏗️ ENTERING DOCKER BUILD STEP (NO BUILDX): deploy/steps/2.docker-build-no-buildx.sh"
=======

💻 Examples:
  # Build and push images for changed folders
  ./2.docker-build.sh "workspace/server-api,workspace/web-client" develop
'

START_TIME=$(date +%s)
echo "2️⃣ 🐋 🏗️ ENTERING DOCKER BUILD STEP: deploy/steps/2.docker-build.sh"
>>>>>>> origin/develop

SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
ROOT_DIR=$(dirname "$(dirname "$SCRIPT_DIR")")

cleanup() {
  echo "🧹 Performing cleanup..."
  # Kill any remaining background processes
  jobs -p | xargs -r kill
<<<<<<< HEAD
=======
  # Optional: cleanup temporary files/directories
  if [ -d "${BUILDX_CACHE_PATH}-new" ]; then
    rm -rf "${BUILDX_CACHE_PATH}-new"
  fi
>>>>>>> origin/develop
}

trap cleanup EXIT

CHANGED_FOLDERS_CSV=$1
IFS=',' read -ra CHANGED_FOLDERS <<< "$CHANGED_FOLDERS_CSV"
tag=$2
web_client_host=$3

<<<<<<< HEAD
=======
# Set base cache path with environment
BASE_CACHE_PATH="/tmp/.buildx-cache"
BUILDX_CACHE_PATH="${BASE_CACHE_PATH}-${ENVIRONMENT}"

# Ensure cache directory exists
mkdir -p "${BUILDX_CACHE_PATH}" || echo "⚠️ Failed to create cache directory."

>>>>>>> origin/develop
if [ -z "$CHANGED_FOLDERS_CSV" ]; then
  echo "❌ No folders specified"
  echo "Usage: $0 <changed_folders_csv> <tag> <web_client_host>"
  exit 1
fi

if [ -z "$web_client_host" ]; then
  echo "⚠️ No web_client_host specified."
fi

<<<<<<< HEAD
=======
# Set debug flag based on GitHub Actions debug mode
DEBUG_FLAG=""
if [ "${ACTIONS_RUNNER_DEBUG:-}" = "1" ] ||
  [ "${RUNNER_DEBUG:-}" = "1" ] ||
  [ "${ACTIONS_STEP_DEBUG:-}" = "1" ]; then
    DEBUG_FLAG="--debug"
    echo "🐛 Debug mode enabled for docker buildx."
fi

>>>>>>> origin/develop
# Check if tag is provided
if [ -z "$tag" ]; then
    tag="latest"
fi

<<<<<<< HEAD
=======
# if [ -z "${BUILDX_CACHE_PATH}" ]; then
#   echo "❌ BUILDX_CACHE_PATH environment variable is not set"
#   exit 1
# fi

echo "🔄 Setting up Docker buildx..."
if ! docker buildx inspect multiarch-builder >/dev/null 2>&1; then
    docker buildx create --name multiarch-builder --driver-opt network=host || true
fi
docker buildx use multiarch-builder || {
  echo "❌ Failed to set up Docker buildx."
  exit 1
}

function updateBuildCache() {
    local cache_name="$1"
    local specific_cache_path="${BUILDX_CACHE_PATH}-${cache_name}"

    if [ -d "${specific_cache_path}-new" ]; then
        echo "🔄 Updating buildx cache for ${cache_name} (${ENVIRONMENT})..."
        rm -rf "${specific_cache_path}" || echo "⚠️ Failed to remove old cache."
        mv "${specific_cache_path}-new" "${specific_cache_path}" || echo "⚠️ Failed to move new cache."
        echo "✅ Cache updated for ${cache_name} (${ENVIRONMENT})"
    else
        echo "⚠️ No new cache generated for ${cache_name} (${ENVIRONMENT})"
    fi
}

>>>>>>> origin/develop
# Call service-info.js to get global configuration values
DOCKER_ORG=$(node "$SCRIPT_DIR/../util/service-info.js" getConfigGlobal docker_org)

echo "ℹ️ Debug values:"
echo "ℹ️ DOCKER_ORG=$DOCKER_ORG"
echo "ℹ️ TAG=$tag"

if [ -z "$DOCKER_ORG" ]; then
    echo "❌ DOCKER_ORG not set, fetching..."
    DOCKER_ORG=$(node "$SCRIPT_DIR/../util/service-info.js" getConfigGlobal docker_org)
    if [ -z "$DOCKER_ORG" ]; then
        echo "❌ Failed to get DOCKER_ORG."
        exit 1
    fi
    echo "✅ DOCKER_ORG set to: $DOCKER_ORG"
fi

RESOURCES_IMAGE="us-docker.pkg.dev/$DOCKER_ORG/gcr.io/divinci-resources:$tag"

function buildRegistryUrl {
  local folder=$1
  local CONTAINER_NAME

  CONTAINER_NAME="$(node "$SCRIPT_DIR/../util/service-info.js" getServiceContainerName "$folder" "$tag")"
  echo "us-docker.pkg.dev/$DOCKER_ORG/gcr.io/$CONTAINER_NAME:$tag"
}

function buildServiceImage {
    local folder=$1

    # Skip resource folders as they're handled by resources.ci.Dockerfile
    if [[ "$folder" == */resources/* ]]; then
        echo "⏭️ Skipping resource folder build for: $folder (handled by resources image)."
        return 0
    fi

<<<<<<< HEAD
=======
    # Generate unique cache path using the base folder name
    local base_folder_name
    base_folder_name=$(basename "$folder")
    local specific_cache_path="${BUILDX_CACHE_PATH}-${base_folder_name}"

    # Create cache directory if it doesn't exist
    mkdir -p "${specific_cache_path}" || echo "⚠️ Failed to create specific cache directory."

>>>>>>> origin/develop
    # Get all Docker build information in one call
    local build_info
    build_info=$(node "$SCRIPT_DIR/../util/service-info.js" getDockerBuildInfo "$folder" "$tag")
    
    # Parse the JSON response
    local base_name=$(echo "$build_info" | jq -r '.containerName')
    local dockerfile_path=$(echo "$build_info" | jq -r '.dockerFile')
    local port=$(echo "$build_info" | jq -r '.port')
    local description=$(echo "$build_info" | jq -r '.description')
    local registry_url=$(echo "$build_info" | jq -r '.registryUrl')
    local docker_org=$(echo "$build_info" | jq -r '.dockerOrg')

<<<<<<< HEAD
    echo "🔵 Building $registry_url image (standard Docker)..."
=======
    echo "🔵 Building $registry_url image..."
>>>>>>> origin/develop
    echo "base_name=$base_name"
    echo "ROOT_DIR=$ROOT_DIR"
    echo "dockerfile=$dockerfile_path"
    echo "APP_FOLDER=$folder"
    echo "PORT=$port"
    echo "ENVIRONMENT=$tag"
    echo "WEB_CLIENT_HOST=$web_client_host"
    echo "DESCRIPTION=$description"
<<<<<<< HEAD
    echo "===================="

    EXTRA_BUILD_ARGS=""

    # Build the Docker image using standard Docker build
    docker build \
        --platform linux/amd64 \
=======
    echo "BUILDX_CACHE_PATH=$BUILDX_CACHE_PATH"
    echo "===================="

    # Build the Docker image using buildx with conditional debug flag
    docker build ${DEBUG_FLAG} \
         multiarch-builder \
        --platform linux/amd64 \
         type=local,src="${specific_cache_path}" \
         type=local,dest="${specific_cache_path}-new",mode=max \
         \
        --progress=plain \
>>>>>>> origin/develop
        -t "${registry_url}" \
        -f "${ROOT_DIR}/${dockerfile_path}" "${ROOT_DIR}" \
        --build-arg DOCKER_ORG="${docker_org}" \
        --build-arg TAG="${tag}" \
        --build-arg APP_FOLDER="${folder}" \
        --build-arg PORT="${port}" \
        --build-arg ENVIRONMENT="${tag}" \
        --build-arg WEB_CLIENT_HOST="${web_client_host}" \
<<<<<<< HEAD
        --build-arg DESCRIPTION="${description}" \
        ${EXTRA_BUILD_ARGS}

    # Push the image to the registry
    echo "🔄 Pushing image to registry: ${registry_url}"
    docker push "${registry_url}"

    echo "🟢 Successfully Built and Pushed $registry_url"
=======
        --build-arg DESCRIPTION="${description}"

    # Update cache with service-specific name
    updateBuildCache "$base_folder_name"

    echo "🟢 Successfully Built $REGISTRY_URL"
>>>>>>> origin/develop
}

function hasResourcesChanges() {
    local changed_folders=("$@")
    for folder in "${changed_folders[@]}"; do
        if [[ "$folder" == *"resources"* ]]; then
            echo "📦 Found resources change in: $folder"
            return 0
        fi
    done
    return 1
}

if hasResourcesChanges "${CHANGED_FOLDERS[@]}"; then
<<<<<<< HEAD
    echo "🏗️ Building resources image:"
    echo "  📂 Context: $ROOT_DIR"
    echo "  📄 Dockerfile: $ROOT_DIR/deploy/docker/ci/resources.ci.Dockerfile"
    echo "  🌍 Environment: ${ENVIRONMENT}"

    # Build the resources image using standard Docker build
    echo "🔵 Building resources image..."
    docker build \
        --platform linux/amd64 \
=======
    echo "🏗️ Building resources image with:"
    echo "  📂 Context: $ROOT_DIR"
    echo "  📄 Dockerfile: $ROOT_DIR/deploy/docker/ci/resources.ci.Dockerfile"
    echo "  💾 Cache path: ${BUILDX_CACHE_PATH}"
    echo "  🌍 Environment: ${ENVIRONMENT}"

    # Build the Docker image using buildx with conditional debug flag
    echo "🔵 Building resources image..."
    docker build ${DEBUG_FLAG} \
         multiarch-builder \
        --platform linux/amd64 \
         "type=local,src=${BUILDX_CACHE_PATH}-resources" \
         "type=local,dest=${BUILDX_CACHE_PATH}-resources-new,mode=max" \
        --push \
        --progress=plain \
>>>>>>> origin/develop
        --build-arg DOCKER_ORG="$DOCKER_ORG" \
        --build-arg TAG="$tag" \
        -t "${RESOURCES_IMAGE}" \
        -f "$ROOT_DIR/deploy/docker/ci/resources.ci.Dockerfile" \
        "$ROOT_DIR"

<<<<<<< HEAD
    # Push the resources image
    echo "🔄 Pushing resources image to registry: ${RESOURCES_IMAGE}"
    docker push "${RESOURCES_IMAGE}"

    build_status=$?
    if [ $build_status -eq 0 ]; then
        echo "✅ Resources image built and pushed successfully!"
=======
    # Update cache with resources-specific name
    updateBuildCache "resources"

    echo "🔄 Pushing resources image to GCR..."
    echo "📦 Image: ${RESOURCES_IMAGE}"

    # Move the new cache after resources build
    updateBuildCache "resources-${ENVIRONMENT}"

    build_status=$?
    if [ $build_status -eq 0 ]; then
        echo "✅ Resources image built successfully!"
>>>>>>> origin/develop
    else
        echo "❌ Resources image build failed with status: $build_status"
        exit $build_status
    fi
else
    echo "📝 No resources changes detected, skipping resources image build."

    # Verify the image exists in GCR
    if ! gcloud container images describe "${RESOURCES_IMAGE}" >/dev/null 2>&1; then
        echo "❌ Required resources image not found in GCR: ${RESOURCES_IMAGE}"
        echo "🔄 Building resources image as it doesn't exist..."

<<<<<<< HEAD
        docker build \
            --platform linux/amd64 \
            --build-arg DOCKER_ORG="$DOCKER_ORG" \
            --build-arg TAG="$tag" \
=======
        docker build ${DEBUG_FLAG} \
             multiarch-builder \
            --build-arg DOCKER_ORG="$DOCKER_ORG" \
            --build-arg TAG="$tag" \
            --platform linux/amd64 \
             "type=local,src=${BUILDX_CACHE_PATH}" \
             "type=local,dest=${BUILDX_CACHE_PATH}-new,mode=max" \
            --push \
            --progress=plain \
>>>>>>> origin/develop
            -t "${RESOURCES_IMAGE}" \
            -f "$ROOT_DIR/deploy/docker/ci/resources.ci.Dockerfile" \
            "$ROOT_DIR"

<<<<<<< HEAD
        # Push the resources image
        echo "🔄 Pushing resources image to registry: ${RESOURCES_IMAGE}"
        docker push "${RESOURCES_IMAGE}"

        build_status=$?
        if [ $build_status -eq 0 ]; then
            echo "✅ Resources image built and pushed successfully!"
=======
        build_status=$?
        if [ $build_status -eq 0 ]; then
            echo "✅ Resources image built successfully!"
            updateBuildCache "resources"
>>>>>>> origin/develop
        else
            echo "❌ Resources image build failed with status: $build_status"
            exit $build_status
        fi
    else
        echo "✅ Resources image already exists in GCR."
    fi
fi

echo "✅ Resources image verified!"

pids=()
CHANGED_FOLDERS_LENGTH=${#CHANGED_FOLDERS[@]}
echo "🔄 Starting parallel builds for ${CHANGED_FOLDERS_LENGTH} folders..."
for ((num=0; num<CHANGED_FOLDERS_LENGTH; num++)); do
  echo "🏗️ Starting build for folder: ${CHANGED_FOLDERS[$num]}"
  buildServiceImage "${CHANGED_FOLDERS[$num]}" &
  pids[num]=$!
  echo "📝 Build process ${pids[num]} started for ${CHANGED_FOLDERS[$num]}"
done

# Update the wait section
echo "⏳ Waiting for all builds to complete..."
for ((num=0; num<CHANGED_FOLDERS_LENGTH; num++)); do
  if wait ${pids[num]}; then
    echo "✅ Build for ${CHANGED_FOLDERS[$num]} completed successfully."
  else
    echo "❌ Build for ${CHANGED_FOLDERS[$num]} failed."
    exit 1
  fi
done

<<<<<<< HEAD
# Clean up Docker images to free up space
echo "🧹 Cleaning up Docker images..."
docker image prune -f

=======
>>>>>>> origin/develop
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
DURATION_MINS=$((DURATION / 60))
DURATION_SECS=$((DURATION % 60))
echo "🕒 Total build time: ${DURATION_MINS}m ${DURATION_SECS}s (${DURATION} seconds total)."
