/**
 * Build and deploy module for the Divinci Deployment CLI
 *
 * This module handles building and deploying services to Google Cloud Run.
 */

const path = require('path');
const { execSync, spawn } = require('child_process');
const fs = require('fs');
const {
  hasResourcesChanges,
  getResourcesImageName,
  getDockerImageName
} = require('./service-utils');

/**
 * Build and deploy services
 *
 * @param {Object} options - Build and deploy options
 * @param {string} options.environment - Environment (develop, stage, prod)
 * @param {string[]} options.serviceFolders - Service folders to build and deploy
 * @param {boolean} options.skipTests - Skip tests
 * @param {boolean} options.yes - Auto-confirm all prompts
 * @returns {Promise<void>}
 */
async function buildAndDeploy(options) {
  const { environment, serviceFolders, skipTests, yes } = options;

  // Run pnpm install to ensure dependencies are up to date
  console.log('📦 Running pnpm install to update dependencies...');
  try {
    const { execSync } = require('child_process');
    execSync('pnpm install', { stdio: 'inherit' });
    console.log('✅ Dependencies updated successfully.');
  } catch (error) {
    console.warn(`⚠️ Warning: Failed to update dependencies: ${error.message}`);
    console.warn('⚠️ Continuing with deployment anyway...');
  }

  // Check if we need to build resources first
  const needsResourcesBuild = await shouldBuildResources(serviceFolders);
  if (needsResourcesBuild) {
    await buildResourcesImage(environment);
  }

  // Build and deploy services in parallel
  const buildPromises = serviceFolders.map(folder => {
    // Skip resources folder as it's built separately above
    if (folder.includes('resources')) {
      console.log(`⏩ Resources already built separately: ${folder}`);
      return Promise.resolve();
    }

    return buildAndDeployService(folder, environment, yes);
  });

  // Wait for all builds to complete
  await Promise.all(buildPromises);
}

/**
 * Check if resources should be built
 *
 * @param {string[]} serviceFolders - Service folders
 * @returns {Promise<boolean>} True if resources should be built
 */
async function shouldBuildResources(serviceFolders) {
  // Check if resources are in the list of services to deploy
  const includesResources = serviceFolders.some(folder => folder.includes('resources'));

  // If resources are explicitly included, build them
  if (includesResources) {
    return true;
  }

  // Check if resources have changes
  const hasChanges = await hasResourcesChanges();

  // Build resources if they have changes
  return hasChanges;
}

/**
 * Build the resources image
 *
 * @param {string} environment - Environment (develop, stage, prod)
 * @returns {Promise<void>}
 */
async function buildResourcesImage(environment) {
  console.log('🏗️ Building resources image...');

  try {
    // Get the resources image name
    const resourcesImage = await getResourcesImageName(environment);

    // Build the resources image
    console.log(`  🐳 Building Docker image: ${resourcesImage}`);

    // Use the existing Docker build script
    const buildScript = path.join(process.cwd(), 'deploy/steps/2.docker-build-no-buildx.sh');

    // Make sure the script is executable
    execSync(`chmod +x ${buildScript}`);

    // Run the build script
    execSync(`${buildScript} "workspace/resources" "${environment}"`, {
      stdio: 'inherit'
    });

    console.log('✅ Resources image built successfully.');
  } catch (error) {
    throw new Error(`Failed to build resources image: ${error.message}`);
  }
}

/**
 * Build and deploy a service
 *
 * @param {string} folder - Service folder
 * @param {string} environment - Environment (develop, stage, prod)
 * @param {boolean} autoConfirm - Auto-confirm all prompts
 * @returns {Promise<void>}
 */
async function buildAndDeployService(folder, environment, autoConfirm = false) {
  console.log(`🏗️ Building and deploying service: ${folder}`);

  try {
    // Build the service image
    await buildServiceImage(folder, environment, autoConfirm);

    // Deploy the service
    await deployService(folder, environment, autoConfirm);

    console.log(`✅ Service ${folder} built and deployed successfully.`);
  } catch (error) {
    throw new Error(`Failed to build and deploy service ${folder}: ${error.message}`);
  }
}

/**
 * Build a service image
 *
 * @param {string} folder - Service folder
 * @param {string} environment - Environment (develop, stage, prod)
 * @param {boolean} autoConfirm - Auto-confirm all prompts
 * @returns {Promise<void>}
 */
async function buildServiceImage(folder, environment, autoConfirm = false) {
  console.log(`  🐳 Building Docker image for ${folder}...`);

  try {
    // Use the existing Docker build script
    const buildScript = path.join(process.cwd(), 'deploy/steps/2.docker-build-no-buildx.sh');

    // Make sure the script is executable
    execSync(`chmod +x ${buildScript}`);

    // Run the build script with auto-confirm if yes flag is set
    execSync(`${buildScript} "${folder}" "${environment}"`, {
      stdio: 'inherit',
      env: {
        ...process.env,
        AUTO_CONFIRM: autoConfirm ? 'true' : 'false'
      }
    });

    console.log(`  ✅ Docker image for ${folder} built successfully.`);
  } catch (error) {
    throw new Error(`Failed to build service image for ${folder}: ${error.message}`);
  }
}

/**
 * Deploy a service
 *
 * @param {string} folder - Service folder
 * @param {string} environment - Environment (develop, stage, prod)
 * @param {boolean} autoConfirm - Auto-confirm all prompts
 * @returns {Promise<void>}
 */
async function deployService(folder, environment, autoConfirm = false) {
  console.log(`  🚀 Deploying service ${folder} to ${environment}...`);

  try {
    // Use the existing deploy script
    const deployScript = path.join(process.cwd(), 'deploy/steps/3.deploy-new.sh');

    // Make sure the script is executable
    execSync(`chmod +x ${deployScript}`);

    // Run the deploy script with auto-confirm if yes flag is set
    execSync(`${deployScript} "${folder}" "${environment}"`, {
      stdio: 'inherit',
      env: {
        ...process.env,
        AUTO_CONFIRM: autoConfirm ? 'true' : 'false'
      }
    });

    console.log(`  ✅ Service ${folder} deployed successfully.`);
  } catch (error) {
    throw new Error(`Failed to deploy service ${folder}: ${error.message}`);
  }
}

module.exports = {
  buildAndDeploy
};
