/**
 * Tests for the build and deploy module
 */

const path = require('path');
const { execSync } = require('child_process');
const { buildAndDeploy } = require('../build-deploy');
const serviceUtils = require('../service-utils');

// Mock child_process and service-utils
jest.mock('child_process', () => {
  return {
    execSync: jest.fn()
  };
});

jest.mock('../service-utils', () => {
  return {
    hasResourcesChanges: jest.fn(),
    getResourcesImageName: jest.fn(),
    getDockerImageName: jest.fn()
  };
});

describe('build-deploy', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock service utilities
    serviceUtils.hasResourcesChanges.mockResolvedValue(false);
    serviceUtils.getResourcesImageName.mockResolvedValue('us-docker.pkg.dev/mock-docker-org/gcr.io/divinci-resources:develop');
    serviceUtils.getDockerImageName.mockResolvedValue('us-docker.pkg.dev/mock-docker-org/gcr.io/divinci-develop-web-client:develop');
  });

  describe('buildAndDeploy', () => {
    test('should build resources if explicitly included', async () => {
      await buildAndDeploy({
        environment: 'develop',
        serviceFolders: ['workspace/resources', 'workspace/clients/web'],
        skipTests: true
      });

      // Should call the resources build script
      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('2.docker-build-no-buildx.sh "workspace/resources" "develop"'),
        expect.any(Object)
      );
    });

    test('should build resources if they have changes', async () => {
      serviceUtils.hasResourcesChanges.mockResolvedValue(true);

      await buildAndDeploy({
        environment: 'develop',
        serviceFolders: ['workspace/clients/web'],
        skipTests: true
      });

      // Should call the resources build script
      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('2.docker-build-no-buildx.sh "workspace/resources" "develop"'),
        expect.any(Object)
      );
    });

    test('should not build resources if not needed', async () => {
      serviceUtils.hasResourcesChanges.mockResolvedValue(false);

      await buildAndDeploy({
        environment: 'develop',
        serviceFolders: ['workspace/clients/web'],
        skipTests: true
      });

      // Should not call the resources build script
      expect(execSync).not.toHaveBeenCalledWith(
        expect.stringContaining('2.docker-build-no-buildx.sh "workspace/resources"'),
        expect.any(Object)
      );
    });

    test('should build and deploy services', async () => {
      await buildAndDeploy({
        environment: 'develop',
        serviceFolders: ['workspace/clients/web', 'workspace/servers/public-api'],
        skipTests: true
      });

      // Should call the build script for each service
      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('2.docker-build-no-buildx.sh "workspace/clients/web" "develop"'),
        expect.any(Object)
      );
      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('2.docker-build-no-buildx.sh "workspace/servers/public-api" "develop"'),
        expect.any(Object)
      );

      // Should call the deploy script for each service
      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('3.deploy-new.sh "workspace/clients/web" "develop"'),
        expect.any(Object)
      );
      expect(execSync).toHaveBeenCalledWith(
        expect.stringContaining('3.deploy-new.sh "workspace/servers/public-api" "develop"'),
        expect.any(Object)
      );
    });

    test('should handle build errors', async () => {
      // Mock pnpm install success, then build failure
      execSync.mockImplementationOnce(() => {
        // First call (pnpm install) succeeds
        return '';
      }).mockImplementationOnce(() => {
        // Second call (chmod) succeeds
        return '';
      }).mockImplementationOnce(() => {
        // Third call (build script) fails
        throw new Error('Build failed');
      });

      await expect(buildAndDeploy({
        environment: 'develop',
        serviceFolders: ['workspace/clients/web'],
        skipTests: true
      })).rejects.toThrow('Failed to build');
    });

    test('should handle deploy errors', async () => {
      // Mock build success but deploy failure
      execSync.mockImplementationOnce(() => {
        // First call (pnpm install) succeeds
        return '';
      }).mockImplementationOnce(() => {
        // Second call (chmod for build) succeeds
        return '';
      }).mockImplementationOnce(() => {
        // Third call (build script) succeeds
        return '';
      }).mockImplementationOnce(() => {
        // Fourth call (chmod for deploy) succeeds
        return '';
      }).mockImplementationOnce(() => {
        // Fifth call (deploy script) fails
        throw new Error('Deploy failed');
      });

      await expect(buildAndDeploy({
        environment: 'develop',
        serviceFolders: ['workspace/clients/web'],
        skipTests: true
      })).rejects.toThrow(/Failed to.*deploy/);
    });
  });
});
