import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import morgan from "morgan";

import { createCorsOptions } from "@divinci-ai/server-globals";
import { router as cookieRouter } from "../../util/user-cookie";
import corsDebugMiddleware from "../../middleware/cors-debug";
import mtlsDebugMiddleware from "../../middleware/mtls-debug";

export async function setupHttpApp(){

  const app = express();

  // Add debug middlewares first (only in non-production environments)
  if (process.env.NODE_ENV !== "production") {
    app.use(corsDebugMiddleware);
    app.use(mtlsDebugMiddleware);
  }

  // Use the centralized CORS options from server-globals
  // This ensures consistent CORS configuration across all services
  const corsOptions = createCorsOptions();

  app.use(cors(corsOptions));

  // Use morgan middleware for logging HTTP requests
  morgan.token("req-headers", (req)=>JSON.stringify(req.headers));
  app.use(morgan(":method :url :status :res[content-length] - :response-time ms"));

  app.get("/", function(_, res){
    res.setHeader("Content-Type", "application/json; charset=utf-8");
    res.json({
      "hello": "world",
      "whoami": "an api live server!",
      "server": process.env.HELLO_WORLD
    });
  });

  // Add a specific endpoint for trending
  // Note: We're not setting explicit CORS headers here because the cors middleware handles that
  app.get('/ai-chat/trending', function (req, res) {
    // Log the request
    console.log("📊 Trending request received from:", req.headers.origin);

    // Return sample trending data
    res.json({
      "trending": [
        { "id": "1", "title": "AI Trends", "views": 1200 },
        { "id": "2", "title": "Machine Learning", "views": 980 },
        { "id": "3", "title": "Data Science", "views": 850 }
      ],
      "cors_debug": {
        "origin": req.headers.origin,
        "method": req.method,
        "headers": req.headers
      }
    });
  });

  app.use("/cookie", cookieRouter);

  // Add a middleware to ensure CORS headers are sent even for 404 responses
  app.use((req: Request, res: Response, next: NextFunction) => {
    // If this middleware is reached, it means no route matched
    if (!res.headersSent) {
      // Send 404 response
      // Note: CORS headers are already set by the cors middleware
      res.status(404).json({
        error: {
          message: 'Not Found',
          path: req.path,
          method: req.method
        }
      });
    } else {
      next();
    }
  });

  // Global error handler
  app.use((err: any, req: Request, res: Response, next: NextFunction) => {
    try {
      console.error('🔥 Error:', req.originalUrl, err.message);

      // Note: CORS headers are already set by the cors middleware
      res.status(err.status || 500).json({
        error: {
          message: err.message || 'Internal Server Error',
          cors_debug: {
            origin: req.headers.origin,
            method: req.method
          }
        }
      });
    } catch(e) {
      console.error('🔥 Error in error handler:', e);
      next(e);
    }
  });

  return app;
}
