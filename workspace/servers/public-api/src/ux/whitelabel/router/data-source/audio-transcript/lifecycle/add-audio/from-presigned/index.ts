import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject, castToObject } from "@divinci-ai/utils";
import { createPresignedURL, castPresignedConfig, handlePresignedURL } from "../../../../../../../../util/presigned-r2";
import { DIVINCI_TEST_PROCESS_CONFIG, getUserId } from "@divinci-ai/server-globals";
// import { R2_INSTANCE } from "../../../../../fine-tuning/csv-editor/util";
import { r2 } from "../../../util/r2-constants";

import { transcribeMedia } from "../resuable/transcribe-media";
import { ensureValidMediaName } from "../resuable/validate-media-name";

// Constants
const PRESIGNED_PURPOSE = "AudioTranscript Video Upload";

// Presigned URL preparation endpoint
export const presignedPrepare: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [unCastedBody] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    // Handle both single file and multiple files
    const fileInfo = castShallowObject(unCastedBody, {
      filename: "string",
      byteSize: "number",
    });

    ensureValidMediaName(
      fileInfo.filename, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM
    );

    const presignedUrl = await createPresignedURL(
      r2,
      { userId, purpose: PRESIGNED_PURPOSE },
      fileInfo
    );

    res.statusCode = 200;
    res.json(presignedUrl);
  }catch(e) {
    next(e);
  }
};

// Presigned URL finalization endpoint
export const presignedFinalized: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [unCastedBody, { target, whitelabel }] = await Promise.all([
      castToObject(await jsonBody(req)),
      getWhitelabelTarget(req),
    ]);

    const {
      mediaFile: mediaFileRaw,
    } = castShallowObject(unCastedBody, {
      mediaFile: "string"
    });

    const presignedConfig = castPresignedConfig(mediaFileRaw);

    const presigned = await handlePresignedURL(
      r2,
      { userId, purpose: PRESIGNED_PURPOSE },
      presignedConfig
    );

    const audioFile = await transcribeMedia(
      target, userId, whitelabel._id.toString(),
      { diarizer: diarizerTool, transcriber: transcriberTool },
      presigned,
      {
        filename: presignedConfig.filename,
        sourceType: "file",
        rawValue: presignedConfig.filename,
      },
      void 0,
      DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers)
    );

    // Return immediately with the document ID and status URL
    res.statusCode = 202; // Accepted
    res.json({
      id: doc._id,
      status: doc.processStatus,
      statusUrl: `/white-label/${whitelabel._id}/data-source/audio-transcript/${doc._id}/status`,
      message: "Audio transcription job started. Poll the statusUrl to check progress."
    });
  }catch(e) {
    next(e);
  }
};
