
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { getAudioTools } from "../../../util/audio-tools";
import { getAudioR2Instance } from "@divinci-ai/server-globals";
import { CLOUDFLARE_AUDIO_PUBLIC_URL, LOCAL_AUDIO_PUBLIC_URL, IS_LOCAL_MODE, r2 } from "../../../util/r2-constants";
import { AUDIO_TOOLS } from "@divinci-ai/server-tools";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { getS3Info, copyOriginalToS3 } from "./s3-copy-original";
import { ensureValidFlac } from "./ensure-valid-flac";

// Initialize a backup r2 client for fallbacks
const fallbackR2 = getAudioR2Instance();

// Add this type for local mode options
interface LocalModeOptions {
  useLocalMinio: boolean;
  skipCloudServices: boolean;
  environment: string;
  forceLocalMode?: boolean;
}

import { JSON_Unknown } from "@divinci-ai/utils";

import { DivinciTestProcessConfig } from "@divinci-ai/server-globals";
export async function transcribeMedia(
  target: string, runnerId: string, whitelabelId: string,
  toolConfigs: {
    diarizer: undefined | JSON_Unknown,
    transcriber: undefined | JSON_Unknown,
  },
  originalS3Location: { Bucket: string, Key: string },
  sourceInfo: { filename: string, sourceType: "url" | "file", rawValue: string },
  copiedLocation: { Bucket: string, Key: string } | undefined,
  testConfig: null | DivinciTestProcessConfig
){

  const tools = getAudioTools(toolConfigs);

  const convertArgs = await AUDIO_TOOLS.canConvertToMp3(sourceInfo.filename);
  if(!convertArgs.support){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unsupported file type");
  }

  const { etag, contentLength } = await getS3Info(originalS3Location);
  const [audioS3Location, rawS3Location] = await Promise.all([
    ensureValidMp3(originalS3Location, whitelabelId),
    copiedLocation ? copiedLocation : copyOriginalToS3(originalS3Location, whitelabelId),
  ]);

  // Choose the appropriate public URL based on environment
  // Note: We're now using FLAC files instead of MP3
  const publicUrl = isLocalMode
    ? (LOCAL_AUDIO_PUBLIC_URL || "http://localhost:9000") + "/" + audioS3Location.Key
    : CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioS3Location.Key;

  console.log("📄 Using public URL:", publicUrl);

  // Create a pending transcript record
  const { doc } = await DataSourceAudioTranscriptModel.createTranscript(
    target, runnerId, tools, {
      sourceType: sourceInfo.sourceType,
      mediaType: convertArgs.type,
      sourceId: `${etag}-${contentLength}`,
      info: {
        rawValue: sourceInfo.rawValue,
        rawKey: rawS3Location.Key,
        rawBucket: rawS3Location.Bucket,
        fileType: convertArgs.type,
        filename: sourceInfo.filename,
        hash: etag,
        filesize: contentLength,
        isLocalMode // Add flag for local mode
      }
    },
    {
      Bucket: audioS3Location.Bucket,
      Key: audioS3Location.Key,
      filename: sourceInfo.filename,
      publicUrl: CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioS3Location.Key
    },
    testConfig
  );

  return doc;
}


