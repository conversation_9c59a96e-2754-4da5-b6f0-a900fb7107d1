import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { CastedBusBoy, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { transcribeMedia } from "../resuable/transcribe-media";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { DIVINCI_TEST_PROCESS_CONFIG, getUserId, R2BusBoyFileHandler } from "@divinci-ai/server-globals";

import { r2, IS_LOCAL_MODE } from "../../../util/r2-constants";
// Use a bucket name that doesn't include the hostname in local mode
// In local mode, use a different bucket name that's guaranteed to exist
const BUCKET_NAME = IS_LOCAL_MODE ? "workspace-audio" : "private-temporary-uploads";

console.log(`🪣 Using bucket name: ${BUCKET_NAME}, isLocalMode: ${IS_LOCAL_MODE}`);

// Use the existing r2 client which already has the correct endpoint configuration
// The r2 client from server-globals already handles multiple endpoints in local mode
const audioR2Client = r2;

const fileHandler = new R2BusBoyFileHandler(
  audioR2Client,
  BUCKET_NAME,
  {}, // Empty metadata object
);

const castAudioBody = CastedBusBoy.create(
  {
    mediaFile: "file",
    diarizerTool: "string",
    transcriberTool: "string",
    isLocalMode: "string?", // Make this optional for local mode
  }, fileHandler,
  { mediaFile: (info)=>{
    ensureValidMediaName(info.filename);
    return true;
  } }
);

import { ensureValidMediaName } from "../resuable/validate-media-name";
import { JSON_Unknown } from "@divinci-ai/utils";

export const createAudioTranscriptFromFile: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [{ target, whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      castAudioBody.consumeRequest(req),
    ]);

    const diarizer = tryToParse(body.diarizerTool, "Can't parse diarizerTool");
    const transcriber = tryToParse(body.transcriberTool, "Can't parse transcriber");
    const doc = await transcribeMedia(
      target, userId, whitelabel._id.toString(),
      { diarizer: diarizer, transcriber: transcriber },
      { Bucket: body.mediaFile.bucket, Key: body.mediaFile.objectKey },
      {
        filename: body.mediaFile.info.filename,
        sourceType: "file",
        rawValue: body.mediaFile.info.filename,
      },
      void 0,
      DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers)
    );

    // Return immediately with the document ID and status URL
    res.statusCode = 202; // Accepted
    res.json({
      id: doc._id,
      status: doc.processStatus,
      statusUrl: `/white-label/${whitelabel._id}/data-source/audio-transcript/${doc._id}/status`,
      message: `Audio transcription job started. ${isLocalMode ? 'Using local mode.' : ''} Poll the statusUrl to check progress.`,
      isLocalMode // Include this flag so the frontend knows we're in local mode
    });
  }catch(e){
    console.error("❌ Error in createAudioTranscriptFromFile:", e);
    next(e);
  }
};

function tryToParse(str: string, errorMessage: string): JSON_Unknown{
  try {
    return JSON.parse(str);
  }catch(e){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(errorMessage);
  }
}
