import {
  DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME,
} from "@divinci-ai/models";
import {
  IAudioTranscriptMethods,
  IAudioTranscriptModelType,
  AudioTranscriptStatus,
 } from "../types";

import { RagVectorFileModel } from "../../../rag/file";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { audioTranscriptToRagFile } from "@divinci-ai/tools";

import { lookup as mimeTypeLookup } from "mime-types";

export const generateRagFile: IAudioTranscriptMethods["generateRagFile"] = async function(
  this: InstanceType<IAudioTranscriptModelType>, runnerId, speakers, chunker
){
  const doc = this;

  if(speakers.length === 0){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Need at least one speaker");
  }

  if(doc.processStatus !== AudioTranscriptStatus.Completed){
    throw HTTP_ERRORS_WITH_CONTEXT.LOCKED("Can only make files from completed Audio Transcripts");
  }

  // Get the original audio file's content type for reference
  const originalContentType = mimeTypeLookup(doc.audio.Key);
  if(!originalContentType){
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Audio Transcript has a bad mime type");
  }

  // Always use text/plain for the transcript content
  // This ensures the file is properly processed as text
  const contentType = "text/plain";

  const transcript = (function(){
    try {
      return audioTranscriptToRagFile(doc, speakers);
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM((e as { message: string }).message);
    }
  })();


  const timestamp = new Date();
  const dateString = `${timestamp.getFullYear()}-${timestamp.getMonth() + 1}-${timestamp.getDate()}`;
  const timeString = `${timestamp.getHours()}:${timestamp.getMinutes()}:${timestamp.getSeconds()}`;

  const infoName = getIdentifier(doc);

  // Create a filename with .txt extension to ensure it's processed as text
  const textFilename = doc.audio.filename.replace(/\.[^/.]+$/, "") + ".txt";

  console.log(`📄 Creating RAG file from audio transcript: ${doc._id}`);
  console.log(`📄 Original filename: ${doc.audio.filename}, using: ${textFilename}`);

  // Extract whitelabel ID from the document target
  const docTarget = doc.target || "";
  let whitelabelId = "";
  
  // Try to extract whitelabel ID from the document target
  const targetParts = docTarget.split('/');
  if (targetParts.length >= 1) {
    // Assume the first part is the whitelabel ID
    whitelabelId = targetParts[0];
    console.log(`📄 Extracted whitelabelId from document target: ${whitelabelId}`);
  }
  
  // If we couldn't extract or it's invalid, use a hardcoded value for testing
  if (!whitelabelId || whitelabelId === "default" || whitelabelId === "mongodb") {
    whitelabelId = "682415a03d653676ebe89b06";
    console.log(`📄 Using fallback hardcoded whitelabelId: ${whitelabelId}`);
  }

  if (!whitelabelId) {
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Could not extract whitelabel ID from target");
  }

  // Format the target properly as "mongodb/rag-vector/{whitelabelId}"
  const formattedTarget = `mongodb/rag-vector/${whitelabelId}`;
  console.log(`📄 Using formatted target: ${formattedTarget}`);
  console.log(`📄 Using whitelabel ID: ${whitelabelId}`);

  const { doc: fileDoc, workResult } = await RagVectorFileModel.addNewFileFromText(
    formattedTarget, runnerId,
    {
      source: {
        model: DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME,
        _id: doc._id.toString(),
        title: doc.sourceOrigin.info.rawValue as string,
      },
      filename: textFilename,
      mimeType: contentType,
      text: transcript,
    },
    { chunker },
    {
      title: `Audio: ${doc.sourceOrigin.sourceType} ${infoName} ${dateString} ${timeString}`,
      description: [
        `The chunks related to the audio from ${doc.sourceOrigin.sourceType} related to ${infoName}.`,
        `This file was created on ${dateString} at ${timeString}.`,
        `Changing these chunks will not effect the original audio transcript`
      ].join("\n"),
    },
    null
  );

  return { fileDoc, work: workResult };
};

function getIdentifier(doc: InstanceType<IAudioTranscriptModelType>){
  switch(doc.sourceOrigin.sourceType){
    case "url": return doc.sourceOrigin.sourceId;
    case "file": return doc.sourceOrigin.info.filename as string;
    default: {
      throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Document Malformed");
    }
  }
}
