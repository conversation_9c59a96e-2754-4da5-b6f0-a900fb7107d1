import mongoose from "mongoose";
import { IRagVectorFileModelType } from "../types";
import { RagVectorTextChunksStatus } from "@divinci-ai/models";

export const createFileRecord: IRagVectorFileModelType["createFileRecord"] = async function(
  this: IRagVectorFileModelType, target, r2File, tools, userInfo
){
  // Use timestamp as number for proper sorting
  const saveTimestamp = new Date();
  const timestampNumber = saveTimestamp.getTime();
  
  // Generate the MongoDB _id beforehand so we can use it for both _id and fileId
  const newId = new mongoose.Types.ObjectId();
  console.log(`🔍 [CREATE FILE RECORD] Generated new ObjectId: ${newId.toString()}`);

  const doc = new this({
    _id: newId,
    fileId: newId.toString(), // Set fileId to the same value as _id for compatibility
    target,
    title: userInfo.title,
    description: userInfo.description,
    status: RagVectorTextChunksStatus.CHUNKING,
    uploadTimestamp: timestampNumber,
    updateTimestamp: timestampNumber,
    originalFilename: r2File.originalName,
    rawFileKey: r2File.objectKey,
    fileKey: r2File.objectKey,
    chunkingTool: tools.chunker.id,
  });

  console.log(`🔍 [CREATE FILE RECORD] Creating new record with _id: ${doc._id}, fileId: ${doc.fileId}`);
  await doc.save();
  console.log(`✅ [CREATE FILE RECORD] Successfully created file record with _id: ${doc._id}, fileId: ${doc.fileId}`);
  return { doc };
};
