// Legacy implementation
import { pipeline } from "node:stream/promises";
import {
  IRagVectorFileModelType,
  RagVectorTextChunk,
  RagChunkingTools
} from "../../types";
import { delay } from "@divinci-ai/utils";
import { RagVectorTextChunksStatus } from "@divinci-ai/models";
import { DivinciTestProcessConfig, DIVINCI_TEST_PROCESS_CONFIG as DIVINCI_PROCESS  } from "@divinci-ai/server-globals";

import { WriteToMongo } from "./WriteToMongo";
import { increaseDelayWhenData } from "./increaseDelayWhenData";
import { ToolPendingTransaction } from "../../../../../money";

const FIVE_MINUTES = 1000 * 60 * 5;
const TWO_MINUTES = 1000 * 60 * 2;

import { payAndEscrow } from "./payAndEscrow";
import { RAG_RAW_TO_CHUNKS } from "@divinci-ai/tools";

export const chunkAndSave = async function(
  doc: InstanceType<IRagVectorFileModelType>,
  runnerId: string,
  tools: RagChunkingTools,
  processConfig: null | DivinciTestProcessConfig
){
  const model = doc.constructor as IRagVectorFileModelType;
  try {
    const transactionId = await payAndEscrow(doc, runnerId, tools);
    return {
      work: Promise.resolve().then(async ()=>{
        return doWork(doc, transactionId, tools, processConfig);
      }),
    };
  }catch(e: any){
    console.error("❌ chunkAndSave error:", e);
    await model.findByIdAndUpdate(
      doc._id,
      { $set: {
        status: RagVectorTextChunksStatus.FAILED_TO_CHUNK,
        failureReason: e.message,
      } },
    );
    throw e;
  }
};

async function doWork(
  doc: InstanceType<IRagVectorFileModelType>,
  transactionId: string,
  tools: RagChunkingTools,
  processConfig: null | DivinciTestProcessConfig
){
  const model = doc.constructor as IRagVectorFileModelType;
  try {
    await DIVINCI_PROCESS.runConfig(processConfig);

    const { readable: chunker, cancel } = await tools.chunker.api(
      tools.chunker.config, doc.rawR2Pointer()
    );
    const mongoWriter = new WriteToMongo<
      { text: string },
      Omit<RagVectorTextChunk, "_id">
    >(model, doc._id.toString(), "chunks", ({ text })=>({
      text: text,
      tags: [],
    }));

    await Promise.race([
      pipeline(
        chunker,
        mongoWriter
      ),
      Promise.resolve().then(async ()=>{
        await delay(FIVE_MINUTES);
        try {
          await increaseDelayWhenData(chunker, TWO_MINUTES);
        }catch(e){
          // Cleanup streams after throw
          try { cancel(); }catch(e){ console.error("❌ Error canceling chunker", e); }
          try { chunker.destroy(); }catch(e){ console.error("❌ Error destroying chunker", e); }
          try { mongoWriter.destroy(); }catch(e){ console.error("❌ Error destroying chunker", e); }
          throw new Error("Chunking took too long");
        }
      })
    ]);
    const length = await doc.getChunkLength();
    await model.findByIdAndUpdate(
      doc._id,
      { $set: { status: RagVectorTextChunksStatus.EDITING, indexOffset: length } },
    );
    await ToolPendingTransaction.finalize(
      transactionId, RAG_RAW_TO_CHUNKS, { chunkCount: length }
    );
  }catch(e: any){
    console.error("❌ chunkAndSave error:", e);
    await model.findByIdAndUpdate(
      doc._id,
      { $set: {
        status: RagVectorTextChunksStatus.FAILED_TO_CHUNK,
        failureReason: e.message,
      } },
    );
    await ToolPendingTransaction.failAndReverse(
      transactionId, e
    );
  }
}