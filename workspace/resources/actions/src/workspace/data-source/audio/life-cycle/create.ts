
import {
  replaceParams, fetchBody, handleFetch,
  checkUsePresigned, handleEmptyFetch, PresignedCheck,
  PresignedResult
} from "@divinci-ai/utils";

import { DATA_SOURCE_AUDIO_ROOT } from "../paths";
import { AudioTranscriptDoc, SelectedPublicToolWithConfig } from "@divinci-ai/models";

import { DIVINCI_TEST_PROCESS_Config, DIVINCI_TEST_PROCESS_AddHeader } from "@divinci-ai/utils";

export async function dataSourceAudioCreateFile(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    mediaFile: File,
    diarizerTool: SelectedPublicToolWithConfig,
    transcriberTool: SelectedPublicToolWithConfig,
  },
  presigned: PresignedCheck = "check-size",
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  if(!checkUsePresigned(body.mediaFile.size, presigned)){
    return dataSourceAudioCreateFileDirect(fetcher, params, body, testConfig);
  }
  return dataSourceAudioCreateFilePresigned(fetcher, params, body, testConfig);
}
}

export async function dataSourceAudioCreateURL(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    url: string,
    diarizerTool: SelectedPublicToolWithConfig,
    transcriberTool: SelectedPublicToolWithConfig,
  },
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  return await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/url`, params),
    DIVINCI_TEST_PROCESS_AddHeader(fetchBody("POST", body), testConfig)
  )) as AudioTranscriptDoc;
}

async function dataSourceAudioCreateFileDirect(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    mediaFile: File,
    diarizerTool: SelectedPublicToolWithConfig,
    transcriberTool: SelectedPublicToolWithConfig,
  },
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const formbody = new FormData();
  formbody.append("mediaFile", body.mediaFile);
  formbody.append("diarizerTool", JSON.stringify(body.diarizerTool));
  formbody.append("transcriberTool", JSON.stringify(body.transcriberTool));
  return await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/file`, params),
    DIVINCI_TEST_PROCESS_AddHeader({ method: "POST", body: formbody }, testConfig)
  )) as AudioTranscriptDoc;
}

export async function dataSourceAudioCreateFilePresigned(
  fetcher: typeof fetch, params: { whitelabelId: string },
  body: {
    mediaFile: File,
    diarizerTool: SelectedPublicToolWithConfig,
    transcriberTool: SelectedPublicToolWithConfig,
  },
  testConfig: null | DIVINCI_TEST_PROCESS_Config = null
){
  const prefetchedInfo = await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/presigned/prepare`, params),
    fetchBody("POST", {
      filename: body.mediaFile.name,
      byteSize: body.mediaFile.size,
    })
  )) as PresignedResult;

  await handleEmptyFetch(fetch(prefetchedInfo.url, {
    method: "PUT",
    headers: { "Content-Type": body.mediaFile.type },
    body: body.mediaFile,
  }));

  // Start the transcription job
  const response = await handleFetch(fetcher(
    replaceParams(`${DATA_SOURCE_AUDIO_ROOT}/presigned/finalized`, params),
    DIVINCI_TEST_PROCESS_AddHeader(fetchBody("POST", {
      mediaFile: JSON.stringify(prefetchedInfo),
      diarizerTool: JSON.stringify(body.diarizerTool),
      transcriberTool: JSON.stringify(body.transcriberTool),
    }), testConfig)
  )) as AudioTranscriptDoc;
}
