import { getOpenAI } from "@divinci-ai/server-globals";

import { prepareFileStream, prepareFileBuffer } from "./prepare-file";

import { Readable } from "stream";

export async function useModuleToAddFile(fileId: string, stream: Readable){
  const buffer = await prepareFileBuffer(stream);

  const { openai } = getOpenAI();

  const file = await openai.files.create({
    file: new File([buffer], fileId),
    purpose: "fine-tune",
  });

  return file.id;
}

type OpenAIFileResponse = {
  "id": string,
  "object": "file",
  "bytes": number,
  "created_at": number,
  "filename": string,
  "purpose": "fine-tune",
};

import fetch from "node-fetch-commonjs";
import FormData from "form-data";
import { HTTP_ERRORS_WITH_STACK } from "@divinci-ai/server-utils";
export async function useFetchToAddFile(fileId: string, arrayStream: Readable){

  const bufferStream = prepareFileStream(arrayStream);

  const body = new FormData();
  body.append("purpose", "fine-tune");
  body.append("file", bufferStream, `${fileId}.jsonl`);

  const { openai } = getOpenAI();

  const response = await fetch("https://api.openai.com/v1/files", {
    method: "POST",
    headers: {
      ...body.getHeaders(),
      Authorization: `Bearer ${openai.apiKey}`,
    },
    body: body
  });


  if(!response.ok){
    throw new HTTP_ERRORS_WITH_STACK.SERVER_ERROR("Failed to add file to openai");
  }

  return (await response.json() as OpenAIFileResponse).id;
}
