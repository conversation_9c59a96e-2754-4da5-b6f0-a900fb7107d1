
// import { ElementsToTextChunks } from "./ElementsToTextChunks";
// import { logDebug } from "@divinci-ai/utils";
import { Readable } from "stream";
// import { Parser } from "stream-json";
// import StreamArray from "stream-json/streamers/StreamArray";
import { R2FileLocation } from "../../types";
import { OpenParseConfig } from "@divinci-ai/tools";

import { getWhitelabelVectorR2Instance } from "@divinci-ai/server-globals";
import { requireEnvVar } from "@divinci-ai/server-utils";

const OPENPARSE_API_URL = requireEnvVar("OPENPARSE_API_URL");
console.log("OPENPARSE_API_URL:", OPENPARSE_API_URL);

import fetch from "node-fetch-commonjs";
import FormData from "form-data";
export async function r2FileToChunkStream(
  config: OpenParseConfig,
  r2File: R2FileLocation,
): Promise<{ readable: Readable & { count: number }, cancel: () => void }>{
  const r2Target = {
    Bucket: r2File.bucket,
    Key: r2File.objectKey
  };

  const r2 = getWhitelabelVectorR2Instance();
  const r2Obj = await r2.getObject(r2Target);

  if(!r2Obj.Body) {
    throw new Error("Could not retrieve file body from R2");
  }

  const bodyStream = r2Obj.Body as Readable;
  // Add configuration
  const openparseConfig = {
    minTokens: config.minTokens || 64,
    maxTokens: config.maxTokens || 1024,
    embeddings_provider: config.embeddingsProvider || "cloudflare"
  };

  // Ensure we use a .txt extension for the file sent to open-parse
  // This prevents open-parse from trying to process it as a video/audio file
  const textFilename = r2File.originalName.replace(/\.[^/.]+$/, "") + ".txt";

  console.log(`📄 Original filename: ${r2File.originalName}, sending as: ${textFilename}`);

  const formData = new FormData();
  formData.append("file", bodyStream, textFilename);
  formData.append("config", JSON.stringify(openparseConfig));

  const controller = new AbortController();

  const response = await fetch(OPENPARSE_API_URL, {
    signal: controller.signal,
    method: "POST",
    body: formData
  });

  if(!response.ok) {
    const errorText = await response.text().catch(() => "Could not read error response");
    console.error(`❌ OpenParse server error: ${response.status} ${response.statusText}`);
    console.error(`❌ Error details: ${errorText}`);
    throw new Error(`OpenParse server error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  const readable = response.body;
  if(!readable){
    throw new Error("Unable to get reader from response body");
  }
  (readable as any).count = 0; // Initialize count

  return {
    readable: readable as Readable & { count: number },
    cancel: ()=>{
      // Implement cancellation logic here
      controller.abort();
    }
  };
}
