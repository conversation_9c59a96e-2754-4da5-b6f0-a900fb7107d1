/*eslint no-console: "off"*/
import { Server } from "http";
import { IncomingMessage, ServerResponse } from "http";
import { Express } from "express";
import { setupEnv } from "./globals/env";
import { join as pathJoin } from "path";

console.log("🔧 Setting up environment...");
setupEnv();
console.log("✅ Environment setup complete.");

import { Service } from "./util/service";
import initTest, { Test } from "tap";
// import type { TestOpts, BuiltPlugins } from "@tapjs/test";

import { testServer } from "./globals/test-server";
import { getApiTestSuitesForChangedFolders } from "./api-test-mapping";
import { tokenCache } from "./globals/auth0/token-cache";
import { userGroupPool } from "./util/resource-pool";

import { runTest as aichatRunTest } from "./tests/ai-chat";
import { runTest as whitelabelRunTest } from "./story-test/workbench/whitelabel";
import { runTest as audioTranscriptRunTest } from "./story-test/workbench/data-sources/audio-transcript";
import { runTest as finetuneRunTest } from "./story-test/workbench/finetune";

import { runTest as promptModerationRunTest } from "./story-test/workbench/prompt-moderation";
import { runTest as threadPrefixRunTest } from "./story-test/workbench/thread-prefix";
import { runTest as messagePrefixRunTest } from "./story-test/workbench/message-prefix";
import { runTest as ragRunTest } from "./story-test/workbench/rag";
import { runTest as releaseRunTest } from "./story-test/workbench/release";
// import { runTest as moneyRunTest } from "./tests/money";
import { runTest as moneyE2ERunTest } from "./tests/money-e2e";

import { getTestConfig } from "./globals/test-config";
import { ensureDockerService } from "./globals/docker";
import { __gitroot } from "./constants/paths";
import { delay } from "@divinci-ai/utils";

import { TapTestResult, setupTestReporting, logTestReport } from "./report";

type DockerService = ReturnType<typeof ensureDockerService>;
type ServicePromise = Promise<
  | { server: Server<typeof IncomingMessage, typeof ServerResponse>, port: string, app: Express }
  | void
  | { stdout: string, stderr: string }
>;


// First, define a type for the test function
type TestFunction = (test: Test)=>Promise<any>;


// Define the TEST_SUITES object with proper typing
const TEST_SUITES: Record<string, TestFunction> = {
  "AI Chats": aichatRunTest,
  "White Label": whitelabelRunTest,
  "Fine Tune": finetuneRunTest,
  "Prompt Moderation": promptModerationRunTest,
  "Thread Prefix": threadPrefixRunTest,
  "Message Prefix": messagePrefixRunTest,
  "RAG": ragRunTest,
  "Workspace Release": releaseRunTest,
  "Audio Transcript": audioTranscriptRunTest,
  "Money E2E": moneyE2ERunTest,
};

// Add a health check function
async function waitForService(url: string, maxAttempts = 20): Promise<void>{
  for(let i = 0; i < maxAttempts; i++) {
    try {
      console.log(`🏥 Health check attempt ${i + 1}/${maxAttempts} for ${url}...`);
      const response = await fetch(url);
      if(response.ok) {
        console.log(`✅ Service at ${url} is ready`);
        return;
      }
    }catch(e) {
      if(i === maxAttempts - 1) throw e;
      await new Promise(resolve=>setTimeout(resolve, 1000));
    }
  }
  throw new Error(`Service at ${url} failed to respond after ${maxAttempts} attempts`);
}

Promise.resolve().then(async ()=>{
  const TEST_TIMEOUT = 60 * 60 * 1000; // 1 hour
  // Add this before running the tests
  new Promise((_, reject)=>{
    setTimeout(()=>{
      const timeoutError = new Error(`Test suite timed out after ${TEST_TIMEOUT/1000} seconds`);
      console.error(`
❌ Test suite timed out! Common causes:
1. Docker Desktop may be stalled or not running properly
   - Try checking Docker Desktop status
   - Try quitting and restarting Docker Desktop
2. Services failed to start properly
   - Check Docker logs for more details
3. Tests are taking longer than expected
   - Consider increasing timeout if needed

For Docker-specific issues:
- Check if Docker Desktop is running
- Try 'docker system prune' to clean up resources
- Restart Docker Desktop if needed
`);
      process.exit(1);
    }, TEST_TIMEOUT);
  });
  console.log("🚀 Starting test suite...");
  setupTestReporting(initTest);
  const config = getTestConfig();
  console.log("📝 Test configuration:", {
    ENVIRONMENT: process.env.ENVIRONMENT,
    CHANGED_FOLDERS: process.env.CHANGED_FOLDERS,
    environment: config.environment,
    NODE_ENV: process.env.NODE_ENV
  });

  let dockerService: DockerService | undefined;

  try {
    // Initialize token cache and resource pools
    tokenCache.init();
    console.log(`🔐 Auth0 token cache initialized (${tokenCache.size()} tokens)`);

    // Initialize resource pools
    console.log(`🏊 Resource pools initialized`);

    // Initialize services based on environment
    console.log(`🌐 Setting up services for ${config.environment} environment...`);

    const servicesToStart: ServicePromise[] = [testServer.use()];  // Always start test server

    if(config.environment === "local") {
      console.log("🐳 Setting up Docker services for local testing...");

      console.log("🔍 Docker setup details:");
      const dockerFilePath = pathJoin(__gitroot, "./docker/test-api.yml");
      console.log("📄 Docker file path:", dockerFilePath);
      console.log("🌳 Git root:", __gitroot);
      console.log("📂 Current working directory:", process.cwd());

      dockerService = ensureDockerService(
        "Divinci Test Environment",
        dockerFilePath  // Using the stored path
      );

      console.log("🐋 Docker service created:", {
        isInitialized: dockerService !== undefined,
      });

      // Add error handling around the service start
      try {
        if(dockerService) {
          servicesToStart.push(dockerService.use());
          console.log("✅ Docker service added to start queue");
        } else {
          console.warn("⚠️ Docker service was not initialized");
        }
      }catch(error) {
        console.error("❌ Failed to add Docker service:", error);
        throw error;
      }
    } else {
      console.log(`🌎 Running against remote environment: ${config.environment} - skipping Docker setup`);
    }

    console.log("⏳ Waiting for services to start...");
    await Promise.all(servicesToStart);
    console.log("✅ Services started successfully");
    console.log("🐋 dockerService status: ", dockerService ? "initialized" : "not initialized");

    // Add health checks
    console.log("🏥 Performing health checks...");
    await Promise.all([
      waitForService("http://localhost:18081/"),  // Changed from 18081 to 18084
      // Add other service health check endpoints as needed
    ]);

    console.log("✅ All services started and healthy");

    // tap by default has a timeout, we turn it into a day so that we can use our custom one instead
    initTest.setTimeout(24 * 60 * 60 * 1000);

    // Run tests
    console.log("🔍 Determining test suites to run...");
    const changedFolders = process.env.CHANGED_FOLDERS ?
      process.env.CHANGED_FOLDERS.split(",") : [];

    const testSuitesToRun = changedFolders.length > 0 ?
      getApiTestSuitesForChangedFolders(changedFolders) :
      Object.keys(TEST_SUITES);

    console.log("🧪 Running test suites:", testSuitesToRun);

    // Create a parent test to contain all suites
    await initTest.test("Test Suites", async (parentTest)=>{
      for(const suiteName of testSuitesToRun) {
        const typedSuiteName = suiteName;
        if(typedSuiteName in TEST_SUITES) {
          console.log(`📋 Running ${suiteName} tests...`);
          try {
            await parentTest.test(suiteName, async (test)=>{
              const suiteResult = await TEST_SUITES[typedSuiteName](test);
              console.log(`✅ ${suiteName} completed:`, suiteResult);
            });
          }catch(error: any) {
            console.error(`\n❌ Error in ${suiteName} suite:`);
            console.error("Message:", error.message);
            if(error.cause) console.error("Cause:", error.cause);
            if(error.stack) console.error("Stack:", error.stack);
            throw error;
          }
        }
      }
      parentTest.end();
    });

    // Move initTest.end() here, after all tests are complete
    initTest.end();

  }catch(e: any) {
    console.error("\n❌ Test Suite Error:");
    console.error("Message:", e.message);
    if(e.cause) console.error("Cause:", e.cause);
    if(e.stack) console.error("Stack:", e.stack.split("\n").slice(0, 5).join("\n")); // First 5 lines of stack
    throw e;
  } finally {
      console.log("🧹 Starting cleanup...");

      // Log token cache and resource pool stats
      console.log(`🔐 Auth0 token cache stats: ${tokenCache.size()} tokens cached`);
      console.log(`🏊 Resource pools cleaned up`);

      // Cleanup services
      type CleanupTask = void | Promise<void>;
      const cleanupTasks: CleanupTask[] = [testServer.release()];  // Always cleanup test server
      if(config.environment === "local" && dockerService) {
        cleanupTasks.push(dockerService.release());
      }

      await Promise.all(cleanupTasks.map(task=>Promise.resolve(task)));
      console.log("✅ Services cleaned up");

      console.log("📊 Active Services:", Service.ACTIVE_SERVICES());

      const exitCode = await logTestReport(initTest);

      process.exit(exitCode);
    }
});

// Add global error handlers
process.on("unhandledRejection", (reason, promise)=>{
  console.error("🚨 Unhandled Rejection at:", promise, "reason:", reason);
});

process.on("uncaughtException", (error)=>{
  console.error("🚨 Uncaught Exception:", error);
});
