import React, { useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useWhiteLabel } from "../../../../../data/WhiteLabel";
import { useTrackedValue } from "../../../../../../../../util/react/tracked-value";
import { RagVectorFileDoc, RagVectorPopulated } from "@divinci-ai/models";
import { FileListContainer } from "./FileListContainer";
import styles from "./rag-file.module.css";

import { RagVectorForm } from "../../../RagVector/Root/Form";

import { NewFilesForm } from "../Create/Bulk";


export function RagVectorFileRoot(){
  const { id, whitelabel } = useWhiteLabel();
  const [searchParams, setSearchParams] = useSearchParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [showNotificationBanner, setShowNotificationBanner] = useState(false);

  const { loading, error: trackedError, value: files, update } = useTrackedValue<
    Array<RagVectorFileDoc & RagVectorPopulated>
  >({
    url: `/white-label/${id}/rag-vector/files`,
    eraseLastValue: false,
  });

  // Get active section from URL params
  const activeSection = searchParams.get("section") as "ragVectorForm" | "newFileForm" | null;

  const toggleSection = (section: "ragVectorForm" | "newFileForm")=>{
    if(activeSection === section) {
      searchParams.delete("section");
    } else {
      searchParams.set("section", section);
    }
    setSearchParams(searchParams);
  };

  if(!whitelabel || !files) return null;

  return (
    <div className="container">
      {showNotificationBanner && <div className="notification is-danger">{notificationMessage}</div>}

      <div className="buttons mb-4">
        <button
          className={`create-rag-vector-drodown button ${activeSection === "ragVectorForm" ? "is-primary" : ""}`}
          onClick={() => toggleSection("ragVectorForm")}
        >
          <span>
            Create RAG Vector
          </span>
          <span className="icon is-small">
            <i className={`fas ${activeSection === "ragVectorForm" ? "fa-chevron-up" : "fa-chevron-down"}`}></i>
          </span>
        </button>
        <button
          className={`rag-upload-files-dropdown button ${activeSection === "newFileForm" ? "is-primary" : ""}`}
          onClick={() => toggleSection("newFileForm")}
        >
          <span>
            Upload File(s)
          </span>
          <span className="icon is-small">
            <i className={`fas ${activeSection === "newFileForm" ? "fa-chevron-up" : "fa-chevron-down"}`}></i>
          </span>
        </button>
      </div>

      {activeSection === "ragVectorForm" && (
        <div className="box" style={{ marginTop: "1rem" }}>
          <RagVectorForm />
        </div>
      )}
      {activeSection === "newFileForm" && (
        <div className="box" style={{ marginTop: "1rem" }}>
          <NewFilesForm updateFiles={update} setIsModalOpen={setIsModalOpen} />
        </div>
      )}

      {loading ? (
        <div> ⏳ Please Wait... </div>
      ) : typeof trackedError === "string" ? (
        <div>
          <h3>❌ Failed loading files.</h3>
          <pre>{trackedError}</pre>
        </div>
      ) : (
        <div id="files-table">
          <FileListContainer
            whitelabel={whitelabel}
          />
        </div>
      )}
    </div>
  );
}
