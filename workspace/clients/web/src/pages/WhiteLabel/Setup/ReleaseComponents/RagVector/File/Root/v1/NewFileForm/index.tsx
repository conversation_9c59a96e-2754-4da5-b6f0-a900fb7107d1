import React, { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { InputFileForm } from "./InputFileForm";
import { ChunkingToolSelect } from "../../../Form/ChunkingToolSelect.old";
import { useWhiteLabel } from "../../../../../../data/WhiteLabel";
import { useTrackedValue } from "../../../../../../../../../util/react/tracked-value";
import { submitAddFileWorkflow } from "./submit-add-file-workflow";
import { useAuth0FetchJSON } from "../../../../../../../../../globals/auth0-user";
import { useAuth0 } from "@auth0/auth0-react";
import { RagVectorDoc } from "@divinci-ai/models";
import { ShallowObject } from "@divinci-ai/utils";

import "./new-file-form.css";

export function NewFileForm({
  updateFiles,
  setIsModalOpen,
}: {
  updateFiles: ()=>any,
  setIsModalOpen: (isOpen: boolean)=>any,
}){
  const [searchParams] = useSearchParams();
  const auth0FetchJSON = useAuth0FetchJSON();
  const { getAccessTokenSilently } = useAuth0();
  const { whitelabel } = useWhiteLabel();
  const [inputFiles, setInputFiles] = useState<FileList | null>(null);
  const [chunkingTool, setChunkingTool] = useState<{
    toolId: string,
    config: ShallowObject | null,
  }>({ toolId: searchParams.get("tool") || "", config: null });
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [addError, setAddError] = useState("");
  const [isAdding, setIsAdding] = useState(false);

  const { value: vectors } = useTrackedValue<Array<RagVectorDoc>>({
    url: !whitelabel ? void 0 : `/white-label/${whitelabel._id}/rag-vector`,
  });

  // Update the title when inputFiles changes
  useEffect(()=>{
    if(inputFiles && inputFiles.length > 0 && !title) {
      setTitle(inputFiles[0].name);
    }
  }, [inputFiles]);

  return (
    <>
      <div className="rag-file-form">
        <div className="box">
          <InputFileForm
            value={inputFiles}
            onChange={setInputFiles}
            disabled={isAdding}
            onSubmit={handleSubmit}
          />
        </div>
        <div className="box">
          <label className="label chunking-tool-label">Choose Chunking Tool</label>
          <ChunkingToolSelect
            value={chunkingTool}
            onChange={(newTool)=>(setChunkingTool(newTool))}
            autoselect
          />
        </div>
        <div className="box hidden-box">
          <div>
            <span>File Title:</span>
            <input
              className="input"
              type="text"
              value={title}
              onChange={(e)=>setTitle(e.target.value)}
            />
          </div>
          <div>
            <div>Description:</div>
            <textarea
              className="textarea"
              value={description}
              onChange={(e)=>setDescription(e.target.value)}
            />
          </div>
        </div>
      </div>
      {addError && (
        <div className="notification is-danger mt-4">
          ⚠️ There was an error handling your file.
          <pre>{addError}</pre>
        </div>
      )}
    </>
  );

  async function handleSubmit(){
    try {
      setAddError("");
      setIsAdding(true);
      console.log("Starting file submission process");

      if(!inputFiles || inputFiles.length === 0) {
        console.error("No files selected");
        throw new Error("❌ No files selected.");
      }

      if(!vectors || vectors.length === 0) {
        console.error("No RAG vector configuration found");
        throw new Error("❌ No RAG vector configuration found. Please create a RAG vector first.");
      }

      if(!whitelabel) {
        console.error("No whitelabel found");
        throw new Error("❌ No whitelabel found.");
      }

      const whitelabelId = whitelabel._id;
      console.log(`Using whitelabel ID: ${whitelabelId}`);

      if(!chunkingTool.toolId) {
        console.error("No chunking tool selected");
        throw new Error("❌ No chunking tool selected. Please select a chunking tool.");
      }

      console.log(`Selected chunking tool: ${chunkingTool.toolId}`);
      console.log(`Selected RAG vector: ${vectors[0]._id}`);

      const ragName = `${whitelabel.title}_${vectors[0].title}`.toLowerCase()
        .replace(/[^a-z0-9-]/g, "-")
        .replace(/-+/g, "-")
        .replace(/^-|-$/g, "");
      console.log(`Generated RAG name: ${ragName}`);

      // Import the workflow URL from environment variables
      const cloudflareWorkflowUrl = process.env.CHUNKS_VECTORIZED_WORKFLOW_URL || "http://localhost:8791";
      console.log(`Using workflow URL: ${cloudflareWorkflowUrl}`);

      // Get the auth0 token for API authentication
      const auth0Token = await getAccessTokenSilently();
      console.log("Successfully obtained auth0 token");

      console.log(`Submitting ${inputFiles.length} file(s) to workflow`);
      const result = await submitAddFileWorkflow({
        auth0FetchJSON: auth0FetchJSON,
        fileList: inputFiles,
        vectorizeConfig: {
          accountId: process.env.CLOUDFLARE_ACCOUNT_ID || "14a6fa23390363382f378b5bd4a0f849",
          whitelabelId,
          apiToken: process.env.CLOUDFLARE_API_TOKEN || "",
          auth0Token,
          ragName: ragName,
          ragId: vectors[0]._id.toString(),
        },
        cloudflareWorkflowUrl,
        isBatch: inputFiles.length > 1,
        chunkingTool: {
          toolId: chunkingTool.toolId,
          config: chunkingTool.config || null,
        },
        userInfo: {
          title,
          description,
        },
      });

      console.log("File submission successful:", result);

      // Reset form after successful submission
      setInputFiles(null);
      setTitle("");
      setDescription("");
      setChunkingTool({ toolId: "", config: {} });
      console.log("Refreshing file list");
      await updateFiles();
      console.log("File list refreshed");
    } catch(e: any) {
      console.error("❌ Error during file submission: ", e);
      // Extract error message and provide more user-friendly details if available
      let errorMessage = "";
      if (e.json?.message) {
        errorMessage = e.json.message;
      } else if (e.message) {
        errorMessage = e.message;
      } else if (typeof e === 'string') {
        errorMessage = e;
      } else {
        errorMessage = "Unknown error occurred";
      }
      
      // Enhance error message if it's a common issue
      if (errorMessage.includes("Failed to fetch") || errorMessage.includes("Network error")) {
        errorMessage += ". Please check your network connection and try again.";
      } else if (errorMessage.includes("vectorize.index.not_found")) {
        errorMessage += ". The vector index may not be properly configured. Please create a new RAG vector or contact support.";
      }
      
      setAddError(errorMessage);
    } finally {
      setIsAdding(false);
    }
  }
}
