import React from "react";

import { SelectedPublicToolWithConfig } from "@divinci-ai/models";
import { InputProps } from "../../../../../../util/react/input";
import { PublicToolInfoDisplay } from "../../../../../../components/displays/PublicToolInfo";
import { useAudioTools } from "../data/AudioTools";
import styles from "./transcribeTool.module.css";

// Define the ToolInfo interface
interface ToolInfo {
  title: string;
  [key: string]: any;
}

// Constants for tool IDs
const OPENAI_WHISPER_ID = "openai/whisper-1";
const CLOUDFLARE_WHISPER_ID = "@cf/openai/whisper-large-v3-turbo";

export function TranscribeTool(
  { value, onChange, input }: InputProps<SelectedPublicToolWithConfig> & { input?: ProcessAudioInput }
){
  const { value: audioTools } = useAudioTools();

  // Check if an option should be disabled
  const isOptionDisabled = (toolId: string) => {
    return toolId === CLOUDFLARE_WHISPER_ID;
  };

  // Set default tool to OpenAI Whisper
  React.useEffect(() => {
    if (!audioTools) return;
    const { transcribers: available } = audioTools;
    if (Object.entries(available).length === 0) return;
    if (value) return;

    // Set OpenAI Whisper as default if available
    if (available[OPENAI_WHISPER_ID]) {
      onChange(OPENAI_WHISPER_ID);
    } else {
      // Fallback to first available option that's not disabled
      const availableOptions = Object.keys(available).filter(key => !isOptionDisabled(key));
      if (availableOptions.length > 0) {
        onChange(availableOptions[0]);
      } else {
        // If all options are disabled, just use the first one
        onChange(Object.keys(available)[0]);
      }
    }
  }, [audioTools, onChange, value]);

  if (!audioTools) return <div>Loading...</div>;

  // Type assertion for audioTools.transcribers
  const transcribers = audioTools.transcribers as Record<string, ToolInfo>;

  return (
    <div>
      <h1 className="title">Choose your Transcription Tool</h1>
      <select
        className="select"
        value={value}
        onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange(e.target.value)}
      >
        {Object.entries(transcribers).map(([key, toolInfo]) => (
          <option
            key={key}
            value={key}
            disabled={isOptionDisabled(key)}
            className={isOptionDisabled(key) ? styles.disabledOption : ''}
          >
            {toolInfo.title} {isOptionDisabled(key) ? '(Unavailable)' : ''}
          </option>
        ))}
      </select>
      {!value ? null : <PublicToolInfoDisplay tool={transcribers[value] as any} />}
    </div>
  );
}


