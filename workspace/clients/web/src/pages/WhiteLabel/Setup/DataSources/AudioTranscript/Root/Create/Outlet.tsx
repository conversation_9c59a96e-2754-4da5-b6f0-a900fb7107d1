import React from "react";

import {
  PATH_WHITELABEL_DATASOURCE_AUDIO_CREATE_FILE,
  PATH_WHITELABEL_DATASOURCE_AUDIO_CREATE_URL,
  PATH_WHITELABEL_DATASOURCE_AUDIO_CREATE_BULK,
} from "../../paths";
import { replaceParams } from "@divinci-ai/utils";
import { useParams } from "react-router";
import { Link } from "react-router-dom";

// Added comment to test the unit test mapping

const TABS = [
  { title: "Transcribe Bulk", pathname: PATH_WHITELABEL_DATASOURCE_AUDIO_CREATE_BULK },
  { title: "Transcribe URL", pathname: PATH_WHITELABEL_DATASOURCE_AUDIO_CREATE_URL },
  { title: "Transcribe File", pathname: PATH_WHITELABEL_DATASOURCE_AUDIO_CREATE_FILE },
];

import { Outlet } from "react-router";
export function CreateTabsOutlet(){
  const params = useParams();
  return (
    <div>
    <div className="header-tabs tabs is-centered">
      <ul>
        {TABS.map((page)=>{
          const pathname = replaceParams(page.pathname, params);
          return (
            <li
              key={page.title}
              className={window.location.pathname !== pathname ? "" : "is-active"}
            >
              <Link to={pathname}>{page.title}</Link>
            </li>
          );
        })}
      </ul>
    </div>
    <Outlet />
    </div>
  );
}


