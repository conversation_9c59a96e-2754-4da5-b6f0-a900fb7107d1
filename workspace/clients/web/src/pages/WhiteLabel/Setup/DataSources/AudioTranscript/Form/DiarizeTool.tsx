import React from "react";
import { InputProps } from "../../../../../../util/react/input";
import { PublicToolInfoDisplay } from "../../../../../../components/displays/PublicToolInfo";
import { useAudioTools } from "../data/AudioTools";
import { useHealthCheck } from "../../../../../../contexts/HealthCheckContext";
import styles from "./diarizeTool.module.css";

// Define the ToolInfo interface
interface ToolInfo {
  title: string;
  [key: string]: any;
}

// Constants for tool IDs
const OFFICIAL_PYANNOTE_ID = "@pyannote/pyannote-segmentation";
const DIVINCI_PYANNOTE_ID = "@divinci-ai/pyannote-segmentation";

// Separate component for status indicator to avoid hooks in the main component
const StatusIndicator = ({ toolId }: { toolId: string }) => {
  const { serviceStatuses } = useHealthCheck();

  // Map tool IDs to service status keys
  const serviceKey = toolId === OFFICIAL_PYANNOTE_ID ? 'pyannote' :
                     toolId === DIVINCI_PYANNOTE_ID ? 'divinci_pyannote' : null;

  if (!serviceKey) return null;

  const status = serviceStatuses[serviceKey]?.status || 'checking';

  if (status === 'checking') {
    return (
      <div className={styles.statusContainer} title="Checking service status...">
        <div className={styles.spinner} />
        <span className={styles.statusText}>Checking...</span>
      </div>
    );
  } else if (status === 'available') {
    return (
      <div className={styles.statusContainer} title="Service is available">
        <div
          className={styles.statusCircle}
          style={{ backgroundColor: '#48c774' }}
        />
        <span className={styles.statusText}>Ready</span>
      </div>
    );
  } else {
    return (
      <div className={styles.statusContainer} title="Service is unavailable">
        <div
          className={styles.statusCircle}
          style={{ backgroundColor: '#f14668' }}
        />
        <span className={styles.statusText}>Offline</span>
      </div>
    );
  }
};

// Separate component for notifying parent components
const ServiceStatusNotifier = () => {
  const { serviceStatuses } = useHealthCheck();

  // Send message to parent components
  React.useEffect(() => {
    const pyannoteAvailable = serviceStatuses.pyannote?.status === 'available';
    window.postMessage({
      type: 'SERVICE_STATUS_CHANGE',
      available: pyannoteAvailable
    }, '*');
  }, [serviceStatuses]);

  return null;
};

// Separate component for setting default tool
const DefaultToolSetter = ({ value, onChange }: { value: string, onChange: (value: string) => void }) => {
  const { value: audioTools } = useAudioTools();

  React.useEffect(() => {
    if (!audioTools) return;
    const { diarizers: available } = audioTools;
    if (Object.entries(available).length === 0) return;
    if (value) return;

    // Set Official Pyannote as default if available
    if (available[OFFICIAL_PYANNOTE_ID]) {
      onChange(OFFICIAL_PYANNOTE_ID);
    } else {
      // Fallback to first available option
      onChange(Object.keys(available)[0]);
    }
  }, [audioTools, onChange, value]);

  return null;
};

export function DiarizeTool({ value, onChange }: InputProps<string>) {
  const { value: audioTools } = useAudioTools();

  // Check if an option should be disabled
  const isOptionDisabled = (toolId: string) => {
    // return toolId === DIVINCI_PYANNOTE_ID;
    return false;
  };

  if (!audioTools) return <div>Loading...</div>;

  // Type assertion for audioTools.diarizers
  const diarizers = audioTools.diarizers as Record<string, ToolInfo>;

  return (
    <div className="diarizeTool">
      {/* Components with hooks */}
      <DefaultToolSetter value={value} onChange={onChange} />
      <ServiceStatusNotifier />

      <h1 className="title">Choose your Speaker Diarizer</h1>
      <div className="field">
        <div className="control">
          <div className={`select ${styles.selectWithStatus}`}>
            <select
              value={value}
              className={`select ${styles.selectWidth}`}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange(e.target.value)}
            >
              {Object.entries(diarizers).map(([key, toolInfo]) => (
                <option
                  key={key}
                  value={key}
                  disabled={isOptionDisabled(key)}
                  className={isOptionDisabled(key) ? styles.disabledOption : ''}
                >
                  {toolInfo.title} {isOptionDisabled(key) ? '(Unavailable)' : ''}
                </option>
              ))}
            </select>
            <span className={styles.statusIndicator}>
              <StatusIndicator toolId={value} />
            </span>
          </div>
        </div>
      </div>
      {!value ? null : <PublicToolInfoDisplay tool={diarizers[value] as any} />}
    </div>
  );
}
