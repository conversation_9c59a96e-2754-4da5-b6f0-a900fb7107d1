import React, { useCallback, useState } from "react";

import { useRunGetter, GetterState } from "../../../../../../../../util/fetch";
import { useNavigate, useParams, Link } from "react-router-dom";
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";

import { BodyType, runner, processMultipleUrls } from "./types";
import { Form } from "./Form";
import { replaceParams } from "@divinci-ai/utils";
import { PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM, PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT } from "../../../paths";
import {
  AUDIO_SPEAKER_DIARIZATION,
  AUDIO_TRANSCRIPTION,
  getFirstToolConfig,
} from "@divinci-ai/tools";

export function AudioCreateURL(){
  const params = useParams();
  const auth0Fetch = useAuth0Fetch();
  const [body, setBody] = useState<BodyType>({
    url: "",
    urls: [],
    diarizerTool: getFirstToolConfig(AUDIO_SPEAKER_DIARIZATION),
    transcriberTool: getFirstToolConfig(AUDIO_TRANSCRIPTION)
  });
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const { run, state, error } = useRunGetter(
    useCallback(async () => {
      if (typeof params.whitelabelId !== "string") throw new Error("No Whitelabel Available");

      // Check if we have URLs to process
      const hasUrls = body.urls && body.urls.length > 0;
      const hasSingleUrl = !!body.url;

      if (!hasUrls && !hasSingleUrl) throw new Error("Please enter at least one URL");
      if (!body.diarizerTool) throw new Error("Please select a speaker detection tool");
      if (!body.transcriberTool) throw new Error("Please select a speech recognition tool");

      // Simulate upload progress
      setIsUploading(true);
      setUploadProgress(0);

      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 500);

      try {
        // Always use processMultipleUrls which returns an array in both cases
        const results = await processMultipleUrls(
          auth0Fetch,
          { whitelabelId: params.whitelabelId },
          body
        );

        return results;
      } finally {
        clearInterval(progressInterval);
        setUploadProgress(100);
        setTimeout(() => setIsUploading(false), 500);
      }
    }, [auth0Fetch, params.whitelabelId, body])
  );

  const navigate = useNavigate();

  // Form is valid if we have at least one URL and both tools selected
  const isFormValid = ((body.url || (body.urls && body.urls.length > 0)) &&
                      body.diarizerTool &&
                      body.transcriberTool);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const results = await run();

      // If we have multiple results, navigate to the audio transcript list
      if (results.length > 1) {
        navigate(
          replaceParams(
            PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT,
            { whitelabelId: params.whitelabelId }
          )
        );
      } else {
        // For single result, navigate to the specific item
        const doc = results[0];
        navigate(
          replaceParams(
            PATH_WHITELABEL_DATASOURCE_AUDIO_ITEM,
            { whitelabelId: params.whitelabelId, audioId: doc._id }
          )
        );
      }
    } catch (e) {
      console.error("Error:", e);
    }
  };

  return (
    <div className="section">
      <div className="container">
        <nav className="breadcrumb mb-5" aria-label="breadcrumbs">
          <ul>
            <li>
              <Link to={replaceParams(PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT, params)}>
                <span className="icon is-small">
                  <i className="fas fa-headphones" aria-hidden="true"></i>
                </span>
                <span>Audio Transcripts</span>
              </Link>
            </li>
            <li className="is-active">
              <a href="#" aria-current="page">
                <span className="icon is-small">
                  <i className="fas fa-link" aria-hidden="true"></i>
                </span>
                <span>Upload from URLs</span>
              </a>
            </li>
          </ul>
        </nav>

        <form onSubmit={handleSubmit}>
          <Form value={body} onChange={setBody} />

          {error && (
            <div className="notification is-danger mt-5">
              <button className="delete" onClick={() => window.location.reload()}></button>
              <p className="has-text-weight-bold">Error</p>
              <p>{error.toString()}</p>
            </div>
          )}

          {isUploading && (
            <div className="box mt-5">
              <h4 className="subtitle is-5 mb-2">Processing URL...</h4>
              <progress
                className="progress is-primary"
                value={uploadProgress}
                max="100"
              ></progress>
              <p className="has-text-grey">{Math.round(uploadProgress)}% complete</p>
            </div>
          )}

          <div className="field is-grouped mt-5">
            <div className="control">
              <button
                type="submit"
                className={`button is-primary is-large ${state === GetterState.Loading ? 'is-loading' : ''}`}
                disabled={!isFormValid || state === GetterState.Loading}
              >
                <span className="icon">
                  <i className="fas fa-cloud-download-alt"></i>
                </span>
                <span>Process URL{body.urls && body.urls.length > 1 ? 's' : ''}</span>
              </button>
            </div>
            <div className="control">
              <Link
                to={replaceParams(PATH_WHITELABEL_DATASOURCE_AUDIO_ROOT, params)}
                className="button is-light is-large"
              >
                Cancel
              </Link>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
