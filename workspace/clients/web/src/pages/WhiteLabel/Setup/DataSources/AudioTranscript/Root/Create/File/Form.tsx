import React, { useEffect, useState } from "react";

import { StateInputProps } from "../../../../../../../../util/react/input";
import { BodyType } from "./types";
import { useAuth0Fetch } from "../../../../../../../../globals/auth0-user";

import { DiarizeTool } from "../../../Form/DiarizeTool";
import { TranscribeTool } from "../../../Form/TranscribeTool";
import { FileInput } from "../../../Form/MediaInput/FileInput";
import {
  loadAudioToolPreferences,
  saveAudioToolPreferences,
  StorageOption
} from "../../../utils/toolPreferences";

// Import the HealthCheckTester for debugging (only used in development)
import { HealthCheckTester } from "../../../../../../../../components/ServiceHealthCheck/HealthCheckTester";

export function Form({ value, onChange }: StateInputProps<BodyType>) {
  const [preferencesLoaded, setPreferencesLoaded] = useState(false);
  const [savePreferences, setSavePreferences] = useState(true);
  const [storageOption, setStorageOption] = useState<StorageOption>('both');
  const [servicesAvailable, setServicesAvailable] = useState(true);
  const auth0Fetch = useAuth0Fetch();

  // Load saved preferences when component mounts
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // Try to load from both local and server storage
        const prefs = await loadAudioToolPreferences('both', auth0Fetch);

        // Only apply preferences if they exist and we haven't already set values
        if (!preferencesLoaded && (prefs.diarizerTool || prefs.transcriberTool)) {
          onChange(currentValue => ({
            ...currentValue,
            diarizerTool: prefs.diarizerTool || currentValue.diarizerTool,
            transcriberTool: prefs.transcriberTool || currentValue.transcriberTool
          }));
        }

        setPreferencesLoaded(true);
      } catch (error) {
        console.error("Error loading audio tool preferences:", error);
        setPreferencesLoaded(true);
      }
    };

    loadPreferences();
  }, [onChange, auth0Fetch]);

  // Save preferences when they change
  useEffect(() => {
    // Only save if preferences are loaded and the user has selected tools
    if (preferencesLoaded && savePreferences && value.diarizerTool && value.transcriberTool) {
      saveAudioToolPreferences(
        {
          diarizerTool: value.diarizerTool,
          transcriberTool: value.transcriberTool
        },
        storageOption,
        auth0Fetch
      ).catch(error => {
        console.error("Error saving audio tool preferences:", error);
      });
    }
  }, [value.diarizerTool, value.transcriberTool, preferencesLoaded, savePreferences, storageOption, auth0Fetch]);

  const handleDiarizerChange = (newValue: string) => {
    onChange(value => ({ ...value, diarizerTool: newValue }));
  };

  const handleTranscriberChange = (newValue: string) => {
    onChange(value => ({ ...value, transcriberTool: newValue }));
  };

  const toggleSavePreferences = () => {
    setSavePreferences(!savePreferences);
  };

  // We'll get service status from the DiarizeTool component
  useEffect(() => {
    // Listen for messages from DiarizeTool about service status
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'SERVICE_STATUS_CHANGE') {
        setServicesAvailable(event.data.available);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  return (
    <div className="container">
      <div className="card">
        <div className="card-content">
          <h1 className="title is-3 mb-5">Upload Audio or Video</h1>

          {/* Conditionally render the HealthCheckTester in development */}
          {process.env.NODE_ENV === 'development' && <HealthCheckTester />}

          <div className="content">
            <div className="mb-6">
              <FileInput
                value={value.mediaFile ? (Array.isArray(value.mediaFile) ? value.mediaFile : [value.mediaFile]) : null}
                onChange={(newValue) => onChange({ ...value, mediaFile: newValue })}
                // disabled={!servicesAvailable}
              />
              {/* {!servicesAvailable && (
                <p className="help is-danger mt-2">
                  File upload is disabled because one or more required services are unavailable.
                  Please try again later or contact support if the issue persists.
                </p>
              )} */}
            </div>

            <div className="columns">
              <div className="column">
                <div className="box">
                  <h3 className="subtitle is-4 mb-4">
                    <span className="icon-text">
                      <span className="icon">
                        <i className="fas fa-users"></i>
                      </span>
                      <span>Speaker Detection</span>
                    </span>
                  </h3>
                  <DiarizeTool
                    value={value.diarizerTool}
                    onChange={handleDiarizerChange}
                  />
                </div>
              </div>

              <div className="column">
                <div className="box">
                  <h3 className="subtitle is-4 mb-4">
                    <span className="icon-text">
                      <span className="icon">
                        <i className="fas fa-microphone"></i>
                      </span>
                      <span>Speech Recognition</span>
                    </span>
                  </h3>
                  <TranscribeTool
                    value={value.transcriberTool}
                    onChange={handleTranscriberChange}
                  />
                </div>
              </div>
            </div>

            <div className="field mt-4">
              <div className="control">
                <label className="checkbox">
                  <input
                    type="checkbox"
                    checked={savePreferences}
                    onChange={toggleSavePreferences}
                    className="mr-2"
                  />
                  Remember my tool selections for next time
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
