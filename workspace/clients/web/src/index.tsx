import "./polyfills";

import React, { FC, useEffect } from "react";
import "bulma/css/bulma.css";
import "../public/styles.css";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer, useNavigate } from "react-router-dom";
import { MainMenuWrapper } from "./router/MainMenuWrapper";
import { Router } from "./router/Router";
import { Auth0Provider, useAuth0 } from "@auth0/auth0-react";
import {
  AUTH0_CLIENT_ID,
  AUTH0_CLIENT_DOMAIN,
  AUTH0_AUDIENCE,
} from "./globals/constants/auth0";
import { setItem, getItem, removeItem } from "./globals/storage";
import { datadogRum } from "@datadog/browser-rum";
import { JSON_Unknown } from "@divinci-ai/utils";

import { DeviceSizeProvider } from "./globals/device-size";
import { LocalMenuProvider } from "./globals/local-menu";

import { Tooltip } from "react-tooltip";
import ReactMarkdown from "react-markdown";


// Initialize Datadog RUM
datadogRum.init({
  applicationId: process.env.DD_API_ID || "",
  clientToken: process.env.DD_API_KEY || "",
  service: "divinci-web-app",
  site: "us5.datadoghq.com",
  env: process.env.ENVIRONMENT || "development", // Dynamically set environment
  trackUserInteractions: true,
  trackResources: true,
  defaultPrivacyLevel: "mask-user-input",
});

// Start recording after initialization
datadogRum.startSessionReplayRecording();

function initRun(selector: string){
  const root = createRoot(document.querySelector(selector) as Element);

  console.log("🌐AUTH0_CLIENT_DOMAIN:  ", AUTH0_CLIENT_DOMAIN);

  root.render(
    <BrowserRouter>
    <Auth0App />
    </BrowserRouter>
  );
}

function Auth0App(){
  const navigate = useNavigate();

  return (
    <Auth0Provider
      domain={AUTH0_CLIENT_DOMAIN}
      clientId={AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: window.location.origin,
        audience: AUTH0_AUDIENCE,
      }}
      cache={{
        set: async (key, v)=>{
          await setItem(key, v as JSON_Unknown);
        },
        get: async (key)=>{
          const v = await getItem(key);
          if(v === null) return null;
          if(typeof v !== "object") return v;
          const exp = v.decodedToken?.claims?.exp;
          if(typeof exp !== "number") return v;
          if(exp * 1000 < Date.now()) return null;
          return v;
        },
        remove: (key)=>{
          return removeItem(key);
        },
      }}
      onRedirectCallback={(appState)=>{
        if(!appState) return;
        const redirect = appState.redirect;
        if(!redirect) return;
        const url = new URL(redirect);
        navigate(`${url.pathname}${url.search}${url.hash}`);
      }}
    >
      <App />
    </Auth0Provider>
  );
}

import {
  UsersMoneyProvider,
  SavedReleasesProvider,
  ActiveUserGroupProvider
} from "./globals/user-preferences";
import { HealthCheckProvider } from "./contexts/HealthCheckContext";
const App: FC = function(){
  const { isLoading, logout } = useAuth0();

  useEffect(()=>{
    if(isLoading === false) return;
    const timeout = setTimeout(()=>{
      logout({ logoutParams: { returnTo: window.location.origin } });
    }, 1000);

    return ()=>{
      clearTimeout(timeout);
    };
  }, [isLoading]);

  if(isLoading) {
    return <h1> ⏳ Please Wait...  </h1>;
  }

  return (
    <UsersMoneyProvider>
    <ActiveUserGroupProvider>
    <SavedReleasesProvider>
    <DeviceSizeProvider>
      <LocalMenuProvider defaultTitle="Divinci" defaultFooter={null}>
        <MainMenuWrapper>
          <Router />
          <Tooltip
            id="global-tooltip"
            clickable
            style={{ zIndex: 10 }}
            render={({ content })=>(
              <div style={{ maxWidth: "300px" }}>
                <ReactMarkdown>{content}</ReactMarkdown>
              </div>
            )}
          />
          <Tooltip
            id="global-noclick"
            clickable={false}
            style={{ zIndex: 10 }}
            render={({ content })=>(
              <div style={{ maxWidth: "300px" }}>
                <ReactMarkdown>{content}</ReactMarkdown>
              </div>
            )}
          />
        </MainMenuWrapper>
      </LocalMenuProvider>
    </DeviceSizeProvider>
    </SavedReleasesProvider>
    </ActiveUserGroupProvider>
    </UsersMoneyProvider>
  );
};

initRun("#init");
