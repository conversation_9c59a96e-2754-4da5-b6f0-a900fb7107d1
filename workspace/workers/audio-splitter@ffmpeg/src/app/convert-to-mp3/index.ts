
import { s3, getS3Readable, S3Writable } from "../../services/s3";
import { parse as pathParse } from "node:path";
import { uniqueId } from "../../utils/unique";
import { spawnChild, streamToProcessToStream, handleBadExit } from "../../utils/child-process";

import { HTTP_ERRORS_WITH_CONTEXT } from "../../utils/http-errors";

import { FFMPEG_SUPPORTED_INPUT_EXTENSIONS } from "./mime-types";
export * from "./mime-types";

export type ConvertMP3Args = {
  Bucket: string, Key: string,
};

import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import { promisify } from 'util';
import { pipeline } from 'stream/promises';

// Promisified fs functions
const readFile = promisify(fs.readFile);
const unlink = promisify(fs.unlink);
const mkdir = promisify(fs.mkdir);

export async function convertToMP3(
  { Bucket: sourceBucket, Key: sourceKey }: ConvertMP3Args,
  { Bucket: destinationBucket, Key: destinationKey }: ConvertMP3Args
){
  const originalFile = pathParse(sourceKey);
  if(!FFMPEG_SUPPORTED_INPUT_EXTENSIONS.has(
    originalFile.ext.slice(1).toLowerCase()
  )){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unsupported file type");
  }
  const destinationFile = pathParse(destinationKey);

  // Check if this is an MP4 file
  const isMP4 = originalFile.ext.slice(1).toLowerCase() === 'mp4';

  // For MP4 files, we need to use a different approach due to the moov atom issue
  if (isMP4) {
    console.log(`MP4 file detected: ${sourceKey}. Using faststart approach to handle moov atom issue.`);
    return await convertMP4ToFlacWithFastStart(
      { Bucket: sourceBucket, Key: sourceKey },
      { Bucket: destinationBucket, Key: destinationKey }
    );
  }

  // For non-MP4 files, use the standard approach
  console.log(`Converting ${sourceKey} to MP3`);

  /*
  ffmpeg -hide_banner -loglevel error -i pipe:0 -vn -acodec libmp3lame -f mp3 -q:a 2 -write_xing 1 pipe:1
  */

  const ffmpeg = await spawnChild("ffmpeg", [
    // Controls the output
    "-hide_banner",
    "-loglevel", "info", // More verbose logging for debugging

    // Add options to handle problematic files better
    "-err_detect", "ignore_err",
    "-fflags", "+genpts+discardcorrupt",

    // Read input from stdin
    "-i", "pipe:0",

    // Disable video
    "-vn",

    // Set audio options
    "-ar", "44100", // Set sample rate to 44.1kHz
    "-ac", "2",     // Set to stereo

    // Set the audio codec to MP3
    "-acodec", "libmp3lame", "-f", "mp3",

    // High quality variable bitrate
    "-q:a", "2",

    // Make sure the header is written
    "-write_xing", "1",

    // Output to stdout
    "pipe:1"
  ]);
  const { stdin, stdout, stderr } = ffmpeg;
  if(!stdin || !stdout || !stderr){
    ffmpeg.kill();
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Failed to spawn ffmpeg");
  }

  // Capture stderr for better error reporting
  let errorOutput = '';
  stderr.on('data', (data) => {
    const chunk = data.toString();
    errorOutput += chunk;
    console.log(`FFmpeg stderr: ${chunk.trim()}`);
  });

  const source = await getS3Readable(s3, {
    Bucket: sourceBucket, Key: sourceKey
  });

  const destination = new S3Writable(s3, {
    Bucket: destinationBucket,
    Key: `${destinationFile.dir ? destinationFile.dir + "/" : ""}${uniqueId()}.mp3`,
    ContentType: "audio/mpeg",
    Metadata: {}
  });

  try {
    await streamToProcessToStream(
      source, ffmpeg, destination
    );
    return {
      Bucket: destination.config.Bucket,
      Key: destination.config.Key
    };
  } catch(e: any) {
    console.error(`Failed to extract audio to MP3:`, e);

    // Provide more detailed error messages based on the error type
    if (e.name === 'InvalidAccessKeyId') {
      console.error("MinIO authentication error. Check your credentials.");
      throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
        "Failed to authenticate with MinIO. Please check your credentials."
      );
    } else if (e.name === 'NoSuchKey') {
      console.error("File not found in MinIO.");
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND(
        "The requested file was not found in storage."
      );
    } else if (errorOutput.includes("Invalid data found when processing input")) {
      console.error("FFmpeg error output:", errorOutput);
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        `The file appears to be corrupted or not a valid media file. FFmpeg error: ${errorOutput.trim()}`
      );
    } else if (e.message && e.message.includes("ffmpeg")) {
      console.error("FFmpeg processing error:", e.message);
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        `Failed to process the audio/video file: ${e.message}`
      );
    }

    // If it's a generic error, provide a more user-friendly message
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
      `Failed to process the audio/video file: ${e.message || "Unknown error"}`
    );
  }
}

// Special function to handle MP4 files with the moov atom issue and convert to FLAC
async function convertMP4ToFlacWithFastStart(
  { Bucket: sourceBucket, Key: sourceKey }: ConvertMP3Args,
  { Bucket: destinationBucket, Key: destinationKey }: ConvertMP3Args
): Promise<{ Bucket: string, Key: string }> {
  console.log(`Converting MP4 file ${sourceKey} to FLAC using faststart approach`);

  // Create a temporary directory if it doesn't exist
  const tempDir = path.join(os.tmpdir(), 'ffmpeg-temp');
  try {
    await mkdir(tempDir, { recursive: true });
  } catch (err) {
    console.log(`Temp directory ${tempDir} already exists or couldn't be created:`, err);
  }

  // Create temporary file paths
  const tempInputPath = path.join(tempDir, `input-${uniqueId()}.mp4`);
  const tempFaststartPath = path.join(tempDir, `faststart-${uniqueId()}.mp4`);
  const tempOutputPath = path.join(tempDir, `output-${uniqueId()}.flac`);  // Using FLAC for lossless compression with smaller file size

  try {
    console.log(`Downloading ${sourceKey} to ${tempInputPath}`);

    // Download the file from S3
    const source = await getS3Readable(s3, {
      Bucket: sourceBucket, Key: sourceKey
    });

    // Create a write stream to the temporary file
    const writeStream = fs.createWriteStream(tempInputPath);

    // Pipe the S3 stream to the file
    await pipeline(source, writeStream);

    console.log(`Downloaded file to ${tempInputPath}`);

    // Step 1: Convert the MP4 file to faststart format
    console.log(`Converting to faststart format: ${tempFaststartPath}`);

    const faststartArgs = [
      "-hide_banner",
      "-loglevel", "info",
      "-i", tempInputPath,
      "-c", "copy",
      "-map", "0",
      "-movflags", "+faststart",
      tempFaststartPath
    ];

    // Run the faststart conversion
    const faststartProcess = await spawnChild("ffmpeg", faststartArgs);
    await handleBadExit(faststartProcess);

    console.log(`Successfully converted to faststart format: ${tempFaststartPath}`);

    // Step 2: Extract audio from the faststart MP4 to FLAC format
    console.log(`Extracting audio to FLAC: ${tempOutputPath}`);

    const extractArgs = [
      "-hide_banner",
      "-loglevel", "info",
      "-i", tempFaststartPath,
      "-vn",
      "-ar", "44100",  // 44.1kHz sample rate for better quality
      "-ac", "2",      // Stereo audio for better quality
      "-acodec", "flac",  // FLAC codec
      "-compression_level", "8",  // Maximum compression (0-8, where 8 is highest)
      tempOutputPath
    ];

    // Run the audio extraction
    const extractProcess = await spawnChild("ffmpeg", extractArgs);
    await handleBadExit(extractProcess);

    console.log(`Successfully extracted audio to FLAC: ${tempOutputPath}`);

    // Check if the output file exists and has content
    if (!fs.existsSync(tempOutputPath) || fs.statSync(tempOutputPath).size === 0) {
      throw new Error(`Failed to create output file or file is empty`);
    }

    // Read the output file
    const outputBuffer = await readFile(tempOutputPath);
    console.log(`Read FLAC file: ${outputBuffer.length} bytes`);

    // Upload the file to S3
    // Use proper directory structure with year/month/day
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');

    // Extract the whitelabel ID from the destination path if available
    const destinationFile = pathParse(destinationKey);
    const pathParts = destinationFile.dir.split('/');

    // Try to extract whitelabelId from the path
    // Path format is expected to be something like:
    // whitelabelId/audio/YYYY/MM/DD/filename.flac
    const whitelabelId = pathParts[0] || "682415a03d653676ebe89b06"; // Default to hardcoded ID if not found

    // Try to get the original filename from the source key metadata
    // If not available, use the destination key as a fallback
    let originalFilename;
    try {
      // Get the object metadata to retrieve the original filename
      const headResult = await s3.headObject({
        Bucket: sourceBucket,
        Key: sourceKey
      });

      // Check if the metadata contains the original filename
      // Note: R2BusBoyFileHandler stores it as 'originalname' (lowercase)
      if (headResult.Metadata && (headResult.Metadata.originalname || headResult.Metadata.originalName)) {
        originalFilename = headResult.Metadata.originalname || headResult.Metadata.originalName;
        console.log(`✅ Retrieved original filename from metadata: ${originalFilename}`);
      } else {
        // Fallback to using the key
        const sourceKeyParts = sourceKey.split('/');
        originalFilename = sourceKeyParts[sourceKeyParts.length - 1];
        console.log(`⚠️ Original filename not found in metadata, using key: ${originalFilename}`);
      }
    } catch (error) {
      // If we can't get the metadata, use the destination key as a fallback
      const destKeyParts = destinationKey.split('/');
      originalFilename = destKeyParts[destKeyParts.length - 1];
      console.log(`⚠️ Error getting metadata, using destination key: ${originalFilename}`);
    }

    // Get the filename without extension
    const filenameWithoutExt = originalFilename.includes('.')
      ? originalFilename.substring(0, originalFilename.lastIndexOf('.'))
      : originalFilename;

    // Create a properly structured path
    // Use a timestamp prefix to ensure uniqueness, but include the original filename
    const timestamp = Date.now();
    const finalKey = `${whitelabelId}/audio/${year}/${month}/${day}/${timestamp}_${filenameWithoutExt}.flac`;

    console.log(`Creating organized path: ${destinationBucket}/${finalKey}`);

    // Use multiple endpoints for upload if in local mode
    const NODE_ENV = process.env.NODE_ENV || "";
    const ENVIRONMENT = process.env.ENVIRONMENT || "";
    const IS_LOCAL_MODE = NODE_ENV === "development" || NODE_ENV === "local" ||
                        ENVIRONMENT === "local" || ENVIRONMENT === "development";

    if (IS_LOCAL_MODE) {
      console.log("🔄 Running in local mode, trying multiple MinIO endpoints for upload");

      // Get array of possible endpoints
      const minioEndpoints = [
        undefined, // Default client
        "http://local-minio:9000", // Original Docker service name - preferred option
        "http://minio.divinci.local:9000", // DNS alias - fallback
        "http://host.docker.internal:9000",
        "http://localhost:9000",
        "http://127.0.0.1:9000"
      ];

      // Try uploading with each endpoint until one succeeds
      let uploaded = false;
      for (const endpoint of minioEndpoints) {
        if (uploaded) break;

        try {
          console.log(`🔄 Attempting upload to ${finalKey} with endpoint: ${endpoint || 'default'}`);

          // Create a client with the specific endpoint if provided
          const accessKeyId = process.env.MINIO_ROOT_USER || "minioadmin";
          const secretAccessKey = process.env.MINIO_ROOT_PASSWORD || "minioadmin";

          const s3Client = endpoint ? new (s3.constructor as any)({
            endpoint: endpoint,
            credentials: {
              accessKeyId,
              secretAccessKey,
            },
            region: "auto",
            forcePathStyle: true
          }) : s3;

          // Log the endpoint being used for debugging
          console.log(`🔄 Using S3 endpoint for upload: ${endpoint || 'default'} with accessKeyId: ${accessKeyId}`);

          await s3Client.putObject({
            Bucket: destinationBucket,
            Key: finalKey,
            Body: outputBuffer,
            ContentType: "audio/flac"
          });

          console.log(`✅ Successfully uploaded FLAC to ${destinationBucket}/${finalKey} with endpoint: ${endpoint || 'default'}`);
          uploaded = true;
        } catch (error: any) {
          console.warn(`⚠️ Failed to upload to endpoint ${endpoint || 'default'}: ${error?.message || String(error)}`);
        }
      }

      if (!uploaded) {
        // If all endpoints failed, try one more time with the default client
        console.log(`⚠️ All endpoints failed, trying one more time with default client`);
        await s3.putObject({
          Bucket: destinationBucket,
          Key: finalKey,
          Body: outputBuffer,
          ContentType: "audio/flac"
        });
      }
    } else {
      // In production mode, just use the default client
      await s3.putObject({
        Bucket: destinationBucket,
        Key: finalKey,
        Body: outputBuffer,
        ContentType: "audio/flac"
      });
    }

    console.log(`Uploaded FLAC to ${destinationBucket}/${finalKey}`);

    return {
      Bucket: destinationBucket,
      Key: finalKey
    };
  } catch (error: any) {
    console.error("Error in convertMP4ToFlacWithFastStart:", error);

    // Provide more detailed error messages based on the error type
    if (error.name === 'InvalidAccessKeyId') {
      console.error("MinIO authentication error. Check your credentials.");
      throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
        "Failed to authenticate with MinIO. Please check your credentials."
      );
    } else if (error.name === 'NoSuchKey') {
      console.error("File not found in MinIO.");
      throw HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND(
        "The requested file was not found in storage."
      );
    } else if (error.message && error.message.includes("ffmpeg")) {
      console.error("FFmpeg processing error:", error.message);
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        `Failed to process the audio/video file: ${error.message}`
      );
    }

    // If it's a generic error, provide a more user-friendly message
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
      `Failed to process the audio/video file: ${error.message || "Unknown error"}`
    );
  } finally {
    // Clean up temporary files
    try {
      if (fs.existsSync(tempInputPath)) {
        await unlink(tempInputPath);
        console.log(`Deleted temporary input file: ${tempInputPath}`);
      }
    } catch (e) {
      console.warn(`Failed to delete temporary input file: ${e}`);
    }

    try {
      if (fs.existsSync(tempFaststartPath)) {
        await unlink(tempFaststartPath);
        console.log(`Deleted temporary faststart file: ${tempFaststartPath}`);
      }
    } catch (e) {
      console.warn(`Failed to delete temporary faststart file: ${e}`);
    }

    try {
      if (fs.existsSync(tempOutputPath)) {
        await unlink(tempOutputPath);
        console.log(`Deleted temporary output file: ${tempOutputPath}`);
      }
    } catch (e) {
      console.warn(`Failed to delete temporary output file: ${e}`);
    }
  }
}

import {
  FFMPEG_SUPPORTED_VIDEO_EXTENSIONS,
  FFMPEG_SUPPORTED_AUDIO_EXTENSIONS,
} from "./mime-types";
export type SupportedFileArgs = {
  filename: string,
};

export function canHandleFilename({ filename }: SupportedFileArgs){
  const originalFile = pathParse(filename);
  const ext = originalFile.ext.slice(1).toLowerCase();

  if(FFMPEG_SUPPORTED_VIDEO_EXTENSIONS.has(ext)){
    console.log(`Supported video file type: ${ext}`);
    return { support: true, type: "video" };
  }

  if(FFMPEG_SUPPORTED_AUDIO_EXTENSIONS.has(ext)){
    console.log(`Supported audio file type: ${ext}`);
    return { support: true, type: "audio" };
  }

  console.log(`Unsupported file type: ${ext}`);
  return { support: false, type: "unknown" };
}
